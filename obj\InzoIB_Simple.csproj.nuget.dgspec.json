{"format": 1, "restore": {"E:\\it\\inzo ib\\New folder (3)\\New folder (3)\\InzoIB_Simple\\InzoIB_Simple.csproj": {}}, "projects": {"E:\\it\\inzo ib\\New folder (3)\\New folder (3)\\InzoIB_Simple\\InzoIB_Simple.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\it\\inzo ib\\New folder (3)\\New folder (3)\\InzoIB_Simple\\InzoIB_Simple.csproj", "projectName": "InzoIB_v7.4_Simple", "projectPath": "E:\\it\\inzo ib\\New folder (3)\\New folder (3)\\InzoIB_Simple\\InzoIB_Simple.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\it\\inzo ib\\New folder (3)\\New folder (3)\\InzoIB_Simple\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net6.0-windows"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "dependencies": {"Firebase.Auth": {"target": "Package", "version": "[1.0.0, )"}, "FirebaseAuthentication.net": {"target": "Package", "version": "[4.1.0, )"}, "Google.Apis.Auth": {"target": "Package", "version": "[1.68.0, )"}, "Google.Cloud.Firestore": {"target": "Package", "version": "[3.8.0, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "OpenAI": {"target": "Package", "version": "[2.0.0, )"}, "Telegram.Bot": {"target": "Package", "version": "[19.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Host.win-x64", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302\\RuntimeIdentifierGraph.json"}}}}}