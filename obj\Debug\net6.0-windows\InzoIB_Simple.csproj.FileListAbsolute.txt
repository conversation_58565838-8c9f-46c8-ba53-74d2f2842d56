E:\it\inzo ib\New folder (2)\النسخة 44\InzoIB_Simple\bin\Debug\net6.0-windows\InzoIB_v7.4_Simple.exe
E:\it\inzo ib\New folder (2)\النسخة 44\InzoIB_Simple\bin\Debug\net6.0-windows\InzoIB_v7.4_Simple.deps.json
E:\it\inzo ib\New folder (2)\النسخة 44\InzoIB_Simple\bin\Debug\net6.0-windows\InzoIB_v7.4_Simple.runtimeconfig.json
E:\it\inzo ib\New folder (2)\النسخة 44\InzoIB_Simple\bin\Debug\net6.0-windows\InzoIB_v7.4_Simple.dll
E:\it\inzo ib\New folder (2)\النسخة 44\InzoIB_Simple\bin\Debug\net6.0-windows\InzoIB_v7.4_Simple.pdb
E:\it\inzo ib\New folder (2)\النسخة 44\InzoIB_Simple\bin\Debug\net6.0-windows\Newtonsoft.Json.dll
E:\it\inzo ib\New folder (2)\النسخة 44\InzoIB_Simple\bin\Debug\net6.0-windows\OpenAI.dll
E:\it\inzo ib\New folder (2)\النسخة 44\InzoIB_Simple\bin\Debug\net6.0-windows\System.ClientModel.dll
E:\it\inzo ib\New folder (2)\النسخة 44\InzoIB_Simple\bin\Debug\net6.0-windows\System.Diagnostics.DiagnosticSource.dll
E:\it\inzo ib\New folder (2)\النسخة 44\InzoIB_Simple\bin\Debug\net6.0-windows\System.Memory.Data.dll
E:\it\inzo ib\New folder (2)\النسخة 44\InzoIB_Simple\bin\Debug\net6.0-windows\System.Text.Json.dll
E:\it\inzo ib\New folder (2)\النسخة 44\InzoIB_Simple\obj\Debug\net6.0-windows\InzoIB_Simple.csproj.AssemblyReference.cache
E:\it\inzo ib\New folder (2)\النسخة 44\InzoIB_Simple\obj\Debug\net6.0-windows\ApiKeyDialog.baml
E:\it\inzo ib\New folder (2)\النسخة 44\InzoIB_Simple\obj\Debug\net6.0-windows\DividerInputDialog.baml
E:\it\inzo ib\New folder (2)\النسخة 44\InzoIB_Simple\obj\Debug\net6.0-windows\FileAttachmentWindow.baml
E:\it\inzo ib\New folder (2)\النسخة 44\InzoIB_Simple\obj\Debug\net6.0-windows\InputDialog.baml
E:\it\inzo ib\New folder (2)\النسخة 44\InzoIB_Simple\obj\Debug\net6.0-windows\MainWindow.baml
E:\it\inzo ib\New folder (2)\النسخة 44\InzoIB_Simple\obj\Debug\net6.0-windows\ToastWindow.baml
E:\it\inzo ib\New folder (2)\النسخة 44\InzoIB_Simple\obj\Debug\net6.0-windows\ApiKeyDialog.g.cs
E:\it\inzo ib\New folder (2)\النسخة 44\InzoIB_Simple\obj\Debug\net6.0-windows\DividerInputDialog.g.cs
E:\it\inzo ib\New folder (2)\النسخة 44\InzoIB_Simple\obj\Debug\net6.0-windows\FileAttachmentWindow.g.cs
E:\it\inzo ib\New folder (2)\النسخة 44\InzoIB_Simple\obj\Debug\net6.0-windows\InputDialog.g.cs
E:\it\inzo ib\New folder (2)\النسخة 44\InzoIB_Simple\obj\Debug\net6.0-windows\MainWindow.g.cs
E:\it\inzo ib\New folder (2)\النسخة 44\InzoIB_Simple\obj\Debug\net6.0-windows\ToastWindow.g.cs
E:\it\inzo ib\New folder (2)\النسخة 44\InzoIB_Simple\obj\Debug\net6.0-windows\App.g.cs
E:\it\inzo ib\New folder (2)\النسخة 44\InzoIB_Simple\obj\Debug\net6.0-windows\InzoIB_v7.4_Simple_MarkupCompile.cache
E:\it\inzo ib\New folder (2)\النسخة 44\InzoIB_Simple\obj\Debug\net6.0-windows\InzoIB_v7.4_Simple.g.resources
E:\it\inzo ib\New folder (2)\النسخة 44\InzoIB_Simple\obj\Debug\net6.0-windows\InzoIB_Simple.GeneratedMSBuildEditorConfig.editorconfig
E:\it\inzo ib\New folder (2)\النسخة 44\InzoIB_Simple\obj\Debug\net6.0-windows\InzoIB_Simple.AssemblyInfoInputs.cache
E:\it\inzo ib\New folder (2)\النسخة 44\InzoIB_Simple\obj\Debug\net6.0-windows\InzoIB_Simple.AssemblyInfo.cs
E:\it\inzo ib\New folder (2)\النسخة 44\InzoIB_Simple\obj\Debug\net6.0-windows\InzoIB_Simple.csproj.CoreCompileInputs.cache
E:\it\inzo ib\New folder (2)\النسخة 44\InzoIB_Simple\obj\Debug\net6.0-windows\InzoIB_S.D5E77F36.Up2Date
E:\it\inzo ib\New folder (2)\النسخة 44\InzoIB_Simple\obj\Debug\net6.0-windows\InzoIB_v7.4_Simple.dll
E:\it\inzo ib\New folder (2)\النسخة 44\InzoIB_Simple\obj\Debug\net6.0-windows\refint\InzoIB_v7.4_Simple.dll
E:\it\inzo ib\New folder (2)\النسخة 44\InzoIB_Simple\obj\Debug\net6.0-windows\InzoIB_v7.4_Simple.pdb
E:\it\inzo ib\New folder (2)\النسخة 44\InzoIB_Simple\obj\Debug\net6.0-windows\InzoIB_Simple.genruntimeconfig.cache
E:\it\inzo ib\New folder (2)\النسخة 44\InzoIB_Simple\obj\Debug\net6.0-windows\ref\InzoIB_v7.4_Simple.dll
E:\it\inzo ib\New folder (2)\النسخة 44\InzoIB_Simple\obj\Debug\net6.0-windows\SimpleApiKeyDialog.baml
E:\it\inzo ib\New folder (2)\النسخة 44\InzoIB_Simple\obj\Debug\net6.0-windows\SimpleApiKeyDialog.g.cs
E:\it\inzo ib\New folder (3)\InzoIB_Simple\bin\Debug\net6.0-windows\InzoIB_v7.4_Simple.exe
E:\it\inzo ib\New folder (3)\InzoIB_Simple\bin\Debug\net6.0-windows\InzoIB_v7.4_Simple.deps.json
E:\it\inzo ib\New folder (3)\InzoIB_Simple\bin\Debug\net6.0-windows\InzoIB_v7.4_Simple.runtimeconfig.json
E:\it\inzo ib\New folder (3)\InzoIB_Simple\bin\Debug\net6.0-windows\InzoIB_v7.4_Simple.dll
E:\it\inzo ib\New folder (3)\InzoIB_Simple\bin\Debug\net6.0-windows\InzoIB_v7.4_Simple.pdb
E:\it\inzo ib\New folder (3)\InzoIB_Simple\bin\Debug\net6.0-windows\Newtonsoft.Json.dll
E:\it\inzo ib\New folder (3)\InzoIB_Simple\bin\Debug\net6.0-windows\OpenAI.dll
E:\it\inzo ib\New folder (3)\InzoIB_Simple\bin\Debug\net6.0-windows\System.ClientModel.dll
E:\it\inzo ib\New folder (3)\InzoIB_Simple\bin\Debug\net6.0-windows\System.Diagnostics.DiagnosticSource.dll
E:\it\inzo ib\New folder (3)\InzoIB_Simple\bin\Debug\net6.0-windows\System.Memory.Data.dll
E:\it\inzo ib\New folder (3)\InzoIB_Simple\bin\Debug\net6.0-windows\System.Text.Json.dll
E:\it\inzo ib\New folder (3)\InzoIB_Simple\obj\Debug\net6.0-windows\InzoIB_Simple.csproj.AssemblyReference.cache
E:\it\inzo ib\New folder (3)\InzoIB_Simple\obj\Debug\net6.0-windows\ApiKeyDialog.baml
E:\it\inzo ib\New folder (3)\InzoIB_Simple\obj\Debug\net6.0-windows\DividerInputDialog.baml
E:\it\inzo ib\New folder (3)\InzoIB_Simple\obj\Debug\net6.0-windows\FileAttachmentWindow.baml
E:\it\inzo ib\New folder (3)\InzoIB_Simple\obj\Debug\net6.0-windows\InputDialog.baml
E:\it\inzo ib\New folder (3)\InzoIB_Simple\obj\Debug\net6.0-windows\MainWindow.baml
E:\it\inzo ib\New folder (3)\InzoIB_Simple\obj\Debug\net6.0-windows\SimpleApiKeyDialog.baml
E:\it\inzo ib\New folder (3)\InzoIB_Simple\obj\Debug\net6.0-windows\ToastWindow.baml
E:\it\inzo ib\New folder (3)\InzoIB_Simple\obj\Debug\net6.0-windows\ApiKeyDialog.g.cs
E:\it\inzo ib\New folder (3)\InzoIB_Simple\obj\Debug\net6.0-windows\DividerInputDialog.g.cs
E:\it\inzo ib\New folder (3)\InzoIB_Simple\obj\Debug\net6.0-windows\FileAttachmentWindow.g.cs
E:\it\inzo ib\New folder (3)\InzoIB_Simple\obj\Debug\net6.0-windows\InputDialog.g.cs
E:\it\inzo ib\New folder (3)\InzoIB_Simple\obj\Debug\net6.0-windows\MainWindow.g.cs
E:\it\inzo ib\New folder (3)\InzoIB_Simple\obj\Debug\net6.0-windows\SimpleApiKeyDialog.g.cs
E:\it\inzo ib\New folder (3)\InzoIB_Simple\obj\Debug\net6.0-windows\ToastWindow.g.cs
E:\it\inzo ib\New folder (3)\InzoIB_Simple\obj\Debug\net6.0-windows\App.g.cs
E:\it\inzo ib\New folder (3)\InzoIB_Simple\obj\Debug\net6.0-windows\InzoIB_v7.4_Simple_MarkupCompile.cache
E:\it\inzo ib\New folder (3)\InzoIB_Simple\obj\Debug\net6.0-windows\InzoIB_v7.4_Simple.g.resources
E:\it\inzo ib\New folder (3)\InzoIB_Simple\obj\Debug\net6.0-windows\InzoIB_Simple.GeneratedMSBuildEditorConfig.editorconfig
E:\it\inzo ib\New folder (3)\InzoIB_Simple\obj\Debug\net6.0-windows\InzoIB_Simple.AssemblyInfoInputs.cache
E:\it\inzo ib\New folder (3)\InzoIB_Simple\obj\Debug\net6.0-windows\InzoIB_Simple.AssemblyInfo.cs
E:\it\inzo ib\New folder (3)\InzoIB_Simple\obj\Debug\net6.0-windows\InzoIB_Simple.csproj.CoreCompileInputs.cache
E:\it\inzo ib\New folder (3)\InzoIB_Simple\obj\Debug\net6.0-windows\InzoIB_S.D5E77F36.Up2Date
E:\it\inzo ib\New folder (3)\InzoIB_Simple\obj\Debug\net6.0-windows\InzoIB_v7.4_Simple.dll
E:\it\inzo ib\New folder (3)\InzoIB_Simple\obj\Debug\net6.0-windows\refint\InzoIB_v7.4_Simple.dll
E:\it\inzo ib\New folder (3)\InzoIB_Simple\obj\Debug\net6.0-windows\InzoIB_v7.4_Simple.pdb
E:\it\inzo ib\New folder (3)\InzoIB_Simple\obj\Debug\net6.0-windows\InzoIB_Simple.genruntimeconfig.cache
E:\it\inzo ib\New folder (3)\InzoIB_Simple\obj\Debug\net6.0-windows\ref\InzoIB_v7.4_Simple.dll
E:\it\inzo ib\New folder (3)\New folder (3)\InzoIB_Simple\bin\Debug\net6.0-windows\InzoIB_v7.4_Simple.exe
E:\it\inzo ib\New folder (3)\New folder (3)\InzoIB_Simple\bin\Debug\net6.0-windows\InzoIB_v7.4_Simple.deps.json
E:\it\inzo ib\New folder (3)\New folder (3)\InzoIB_Simple\bin\Debug\net6.0-windows\InzoIB_v7.4_Simple.runtimeconfig.json
E:\it\inzo ib\New folder (3)\New folder (3)\InzoIB_Simple\bin\Debug\net6.0-windows\InzoIB_v7.4_Simple.dll
E:\it\inzo ib\New folder (3)\New folder (3)\InzoIB_Simple\bin\Debug\net6.0-windows\InzoIB_v7.4_Simple.pdb
E:\it\inzo ib\New folder (3)\New folder (3)\InzoIB_Simple\bin\Debug\net6.0-windows\Newtonsoft.Json.dll
E:\it\inzo ib\New folder (3)\New folder (3)\InzoIB_Simple\bin\Debug\net6.0-windows\OpenAI.dll
E:\it\inzo ib\New folder (3)\New folder (3)\InzoIB_Simple\bin\Debug\net6.0-windows\System.ClientModel.dll
E:\it\inzo ib\New folder (3)\New folder (3)\InzoIB_Simple\bin\Debug\net6.0-windows\System.Diagnostics.DiagnosticSource.dll
E:\it\inzo ib\New folder (3)\New folder (3)\InzoIB_Simple\bin\Debug\net6.0-windows\System.Memory.Data.dll
E:\it\inzo ib\New folder (3)\New folder (3)\InzoIB_Simple\bin\Debug\net6.0-windows\System.Text.Json.dll
E:\it\inzo ib\New folder (3)\New folder (3)\InzoIB_Simple\obj\Debug\net6.0-windows\InzoIB_Simple.csproj.AssemblyReference.cache
E:\it\inzo ib\New folder (3)\New folder (3)\InzoIB_Simple\obj\Debug\net6.0-windows\ApiKeyDialog.baml
E:\it\inzo ib\New folder (3)\New folder (3)\InzoIB_Simple\obj\Debug\net6.0-windows\DividerInputDialog.baml
E:\it\inzo ib\New folder (3)\New folder (3)\InzoIB_Simple\obj\Debug\net6.0-windows\FileAttachmentWindow.baml
E:\it\inzo ib\New folder (3)\New folder (3)\InzoIB_Simple\obj\Debug\net6.0-windows\InputDialog.baml
E:\it\inzo ib\New folder (3)\New folder (3)\InzoIB_Simple\obj\Debug\net6.0-windows\MainWindow.baml
E:\it\inzo ib\New folder (3)\New folder (3)\InzoIB_Simple\obj\Debug\net6.0-windows\SimpleApiKeyDialog.baml
E:\it\inzo ib\New folder (3)\New folder (3)\InzoIB_Simple\obj\Debug\net6.0-windows\ToastWindow.baml
E:\it\inzo ib\New folder (3)\New folder (3)\InzoIB_Simple\obj\Debug\net6.0-windows\ApiKeyDialog.g.cs
E:\it\inzo ib\New folder (3)\New folder (3)\InzoIB_Simple\obj\Debug\net6.0-windows\DividerInputDialog.g.cs
E:\it\inzo ib\New folder (3)\New folder (3)\InzoIB_Simple\obj\Debug\net6.0-windows\FileAttachmentWindow.g.cs
E:\it\inzo ib\New folder (3)\New folder (3)\InzoIB_Simple\obj\Debug\net6.0-windows\InputDialog.g.cs
E:\it\inzo ib\New folder (3)\New folder (3)\InzoIB_Simple\obj\Debug\net6.0-windows\MainWindow.g.cs
E:\it\inzo ib\New folder (3)\New folder (3)\InzoIB_Simple\obj\Debug\net6.0-windows\SimpleApiKeyDialog.g.cs
E:\it\inzo ib\New folder (3)\New folder (3)\InzoIB_Simple\obj\Debug\net6.0-windows\ToastWindow.g.cs
E:\it\inzo ib\New folder (3)\New folder (3)\InzoIB_Simple\obj\Debug\net6.0-windows\App.g.cs
E:\it\inzo ib\New folder (3)\New folder (3)\InzoIB_Simple\obj\Debug\net6.0-windows\InzoIB_v7.4_Simple_MarkupCompile.cache
E:\it\inzo ib\New folder (3)\New folder (3)\InzoIB_Simple\obj\Debug\net6.0-windows\InzoIB_v7.4_Simple.g.resources
E:\it\inzo ib\New folder (3)\New folder (3)\InzoIB_Simple\obj\Debug\net6.0-windows\InzoIB_Simple.GeneratedMSBuildEditorConfig.editorconfig
E:\it\inzo ib\New folder (3)\New folder (3)\InzoIB_Simple\obj\Debug\net6.0-windows\InzoIB_Simple.AssemblyInfoInputs.cache
E:\it\inzo ib\New folder (3)\New folder (3)\InzoIB_Simple\obj\Debug\net6.0-windows\InzoIB_Simple.AssemblyInfo.cs
E:\it\inzo ib\New folder (3)\New folder (3)\InzoIB_Simple\obj\Debug\net6.0-windows\InzoIB_Simple.csproj.CoreCompileInputs.cache
E:\it\inzo ib\New folder (3)\New folder (3)\InzoIB_Simple\obj\Debug\net6.0-windows\InzoIB_S.D5E77F36.Up2Date
E:\it\inzo ib\New folder (3)\New folder (3)\InzoIB_Simple\obj\Debug\net6.0-windows\InzoIB_v7.4_Simple.dll
E:\it\inzo ib\New folder (3)\New folder (3)\InzoIB_Simple\obj\Debug\net6.0-windows\refint\InzoIB_v7.4_Simple.dll
E:\it\inzo ib\New folder (3)\New folder (3)\InzoIB_Simple\obj\Debug\net6.0-windows\InzoIB_v7.4_Simple.pdb
E:\it\inzo ib\New folder (3)\New folder (3)\InzoIB_Simple\obj\Debug\net6.0-windows\InzoIB_Simple.genruntimeconfig.cache
E:\it\inzo ib\New folder (3)\New folder (3)\InzoIB_Simple\obj\Debug\net6.0-windows\ref\InzoIB_v7.4_Simple.dll
E:\it\inzo ib\New folder (3)\New folder (3)\InzoIB_Simple\bin\Debug\net6.0-windows\Telegram.Bot.dll
E:\it\inzo ib\New folder (3)\New folder (3)\InzoIB_Simple\obj\Debug\net6.0-windows\BotTokenDialog.baml
E:\it\inzo ib\New folder (3)\New folder (3)\InzoIB_Simple\obj\Debug\net6.0-windows\BotTokenDialog.g.cs
E:\it\inzo ib\New folder (3)\New folder (3)\InzoIB_Simple\obj\Debug\net6.0-windows\ExternalNetworkDialog.baml
E:\it\inzo ib\New folder (3)\New folder (3)\InzoIB_Simple\obj\Debug\net6.0-windows\ExternalNetworkDialog.g.cs
E:\it\inzo ib\New folder (3)\New folder (3)\InzoIB_Simple\obj\Debug\net6.0-windows\IPInfoDialog.baml
E:\it\inzo ib\New folder (3)\New folder (3)\InzoIB_Simple\obj\Debug\net6.0-windows\IPInfoDialog.g.cs
E:\it\inzo ib\New folder (3)\New folder (3)\InzoIB_Simple\obj\Debug\net6.0-windows\FirestoreSettingsDialog.baml
E:\it\inzo ib\New folder (3)\New folder (3)\InzoIB_Simple\obj\Debug\net6.0-windows\FirestoreSettingsDialog.g.cs
E:\it\inzo ib\New folder (3)\New folder (3)\InzoIB_Simple\bin\Debug\net6.0-windows\Google.Api.CommonProtos.dll
E:\it\inzo ib\New folder (3)\New folder (3)\InzoIB_Simple\bin\Debug\net6.0-windows\Google.Api.Gax.dll
E:\it\inzo ib\New folder (3)\New folder (3)\InzoIB_Simple\bin\Debug\net6.0-windows\Google.Api.Gax.Grpc.dll
E:\it\inzo ib\New folder (3)\New folder (3)\InzoIB_Simple\bin\Debug\net6.0-windows\Google.Apis.dll
E:\it\inzo ib\New folder (3)\New folder (3)\InzoIB_Simple\bin\Debug\net6.0-windows\Google.Apis.Auth.dll
E:\it\inzo ib\New folder (3)\New folder (3)\InzoIB_Simple\bin\Debug\net6.0-windows\Google.Apis.Core.dll
E:\it\inzo ib\New folder (3)\New folder (3)\InzoIB_Simple\bin\Debug\net6.0-windows\Google.Cloud.Firestore.dll
E:\it\inzo ib\New folder (3)\New folder (3)\InzoIB_Simple\bin\Debug\net6.0-windows\Google.Cloud.Firestore.V1.dll
E:\it\inzo ib\New folder (3)\New folder (3)\InzoIB_Simple\bin\Debug\net6.0-windows\Google.Cloud.Location.dll
E:\it\inzo ib\New folder (3)\New folder (3)\InzoIB_Simple\bin\Debug\net6.0-windows\Google.LongRunning.dll
E:\it\inzo ib\New folder (3)\New folder (3)\InzoIB_Simple\bin\Debug\net6.0-windows\Google.Protobuf.dll
E:\it\inzo ib\New folder (3)\New folder (3)\InzoIB_Simple\bin\Debug\net6.0-windows\Grpc.Auth.dll
E:\it\inzo ib\New folder (3)\New folder (3)\InzoIB_Simple\bin\Debug\net6.0-windows\Grpc.Core.Api.dll
E:\it\inzo ib\New folder (3)\New folder (3)\InzoIB_Simple\bin\Debug\net6.0-windows\Grpc.Net.Client.dll
E:\it\inzo ib\New folder (3)\New folder (3)\InzoIB_Simple\bin\Debug\net6.0-windows\Grpc.Net.Common.dll
E:\it\inzo ib\New folder (3)\New folder (3)\InzoIB_Simple\bin\Debug\net6.0-windows\Microsoft.Bcl.AsyncInterfaces.dll
E:\it\inzo ib\New folder (3)\New folder (3)\InzoIB_Simple\bin\Debug\net6.0-windows\Microsoft.Extensions.DependencyInjection.Abstractions.dll
E:\it\inzo ib\New folder (3)\New folder (3)\InzoIB_Simple\bin\Debug\net6.0-windows\Microsoft.Extensions.Logging.Abstractions.dll
E:\it\inzo ib\New folder (3)\New folder (3)\InzoIB_Simple\bin\Debug\net6.0-windows\System.CodeDom.dll
E:\it\inzo ib\New folder (3)\New folder (3)\InzoIB_Simple\bin\Debug\net6.0-windows\System.Linq.Async.dll
E:\it\inzo ib\New folder (3)\New folder (3)\InzoIB_Simple\bin\Debug\net6.0-windows\System.Management.dll
E:\it\inzo ib\New folder (3)\New folder (3)\InzoIB_Simple\bin\Debug\net6.0-windows\runtimes\win\lib\net6.0\System.Management.dll
E:\it\inzo ib\New folder (3)\New folder (3)\InzoIB_Simple\obj\Debug\net6.0-windows\TestTextWindow.baml
E:\it\inzo ib\New folder (3)\New folder (3)\InzoIB_Simple\obj\Debug\net6.0-windows\TestTextWindow.g.cs
E:\it\inzo ib\New folder (3)\New folder (3)\InzoIB_Simple\obj\Debug\net6.0-windows\DataWindow.baml
E:\it\inzo ib\New folder (3)\New folder (3)\InzoIB_Simple\obj\Debug\net6.0-windows\LoginWindow.baml
E:\it\inzo ib\New folder (3)\New folder (3)\InzoIB_Simple\obj\Debug\net6.0-windows\DataWindow.g.cs
E:\it\inzo ib\New folder (3)\New folder (3)\InzoIB_Simple\obj\Debug\net6.0-windows\LoginWindow.g.cs
