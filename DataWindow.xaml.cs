using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Threading;
using Firebase.Auth;
using System.Diagnostics;

namespace InzoIB_Simple
{
    public partial class DataWindow : Window
    {
        private string userUid;
        private FirebaseAuthLink authLink;
        private FirestoreManager firestoreManager;
        private DispatcherTimer timer;
        private bool hasPermission = true; // افتراضياً المستخدم له صلاحيات

        public DataWindow(string uid, FirebaseAuthLink auth)
        {
            InitializeComponent();
            userUid = uid;
            authLink = auth;
            
            InitializeWindow();
            InitializeFirestore();
            StartTimer();
        }

        /// <summary>
        /// تهيئة النافذة وعرض معلومات المستخدم
        /// </summary>
        private void InitializeWindow()
        {
            try
            {
                // عرض معلومات المستخدم
                UserUidTextBox.Text = userUid;
                
                if (authLink?.User != null)
                {
                    UserEmailTextBox.Text = authLink.User.Email ?? "غير متوفر";
                    UserNameTextBlock.Text = $"👤 مرحباً {authLink.User.Email}";
                }
                else
                {
                    UserEmailTextBox.Text = "غير متوفر";
                    UserNameTextBlock.Text = "👤 مرحباً بك";
                }

                // التحقق من الصلاحيات (يمكن تطويرها لاحقاً)
                CheckUserPermissions();
                
                Debug.WriteLine($"✅ تم تهيئة نافذة البيانات للمستخدم: {userUid}");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ خطأ في تهيئة النافذة: {ex.Message}");
                ShowResult("❌ خطأ في التهيئة", ex.Message, false);
            }
        }

        /// <summary>
        /// تهيئة مدير Firestore
        /// </summary>
        private void InitializeFirestore()
        {
            try
            {
                string firestoreSettingsPath = System.IO.Path.Combine(
                    Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
                    "InzoIB", "firestore_settings.json");
                
                firestoreManager = new FirestoreManager(firestoreSettingsPath);
                
                // ربط الأحداث
                firestoreManager.ConnectionStatusChanged += OnFirestoreConnectionStatusChanged;
                firestoreManager.DataUpdated += OnFirestoreDataUpdated;
                
                Debug.WriteLine("✅ تم تهيئة مدير Firestore في نافذة البيانات");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ خطأ في تهيئة Firestore: {ex.Message}");
            }
        }

        /// <summary>
        /// بدء مؤقت الوقت
        /// </summary>
        private void StartTimer()
        {
            timer = new DispatcherTimer();
            timer.Interval = TimeSpan.FromSeconds(1);
            timer.Tick += Timer_Tick;
            timer.Start();
        }

        /// <summary>
        /// معالج مؤقت الوقت
        /// </summary>
        private void Timer_Tick(object sender, EventArgs e)
        {
            TimeText.Text = DateTime.Now.ToString("yyyy/MM/dd HH:mm:ss");
        }

        /// <summary>
        /// التحقق من صلاحيات المستخدم
        /// </summary>
        private void CheckUserPermissions()
        {
            try
            {
                // هنا يمكن إضافة منطق التحقق من الصلاحيات
                // مثل التحقق من قاعدة بيانات أو ملف إعدادات
                
                // افتراضياً جميع المستخدمين المصادق عليهم لهم صلاحيات
                if (!string.IsNullOrEmpty(userUid))
                {
                    hasPermission = true;
                    AuthStatusTextBlock.Text = "✅ مصادق عليه - صلاحيات كاملة";
                    SendDataButton.IsEnabled = true;
                }
                else
                {
                    hasPermission = false;
                    AuthStatusTextBlock.Text = "❌ غير مصرح - لا توجد صلاحيات";
                    SendDataButton.IsEnabled = false;
                }
                
                Debug.WriteLine($"✅ تم التحقق من الصلاحيات - المستخدم: {(hasPermission ? "مصرح" : "غير مصرح")}");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ خطأ في التحقق من الصلاحيات: {ex.Message}");
                hasPermission = false;
                AuthStatusTextBlock.Text = "❌ خطأ في التحقق من الصلاحيات";
                SendDataButton.IsEnabled = false;
            }
        }

        /// <summary>
        /// معالج حدث زر إرسال البيانات
        /// </summary>
        private async void SendDataButton_Click(object sender, RoutedEventArgs e)
        {
            await SendDataToFirestore();
        }

        /// <summary>
        /// إرسال البيانات إلى Firestore
        /// </summary>
        private async Task SendDataToFirestore()
        {
            try
            {
                // التحقق من الصلاحيات
                if (!hasPermission)
                {
                    ShowResult("❌ رُفض الإرسال", "ليس لديك صلاحيات لإرسال البيانات", false);
                    return;
                }

                // تعطيل الزر أثناء الإرسال
                SendDataButton.IsEnabled = false;
                SendDataButton.Content = "🔄 جاري الإرسال...";

                // إنشاء بيانات تجريبية للإرسال
                var testData = new List<ContentItem>
                {
                    new ContentItem
                    {
                        Id = Guid.NewGuid().ToString(),
                        Title = "بيانات تجريبية من نافذة البيانات",
                        Content = $"تم إرسال هذه البيانات من المستخدم: {userUid}",
                        Category = "test_data",
                        CreatedAt = DateTime.Now,
                        UpdatedAt = DateTime.Now,
                        UserId = userUid
                    }
                };

                // محاولة إرسال البيانات
                bool success = await firestoreManager.SaveDataAsync(testData);

                if (success)
                {
                    ShowResult("✅ نجح الإرسال", 
                        $"تم إرسال البيانات بنجاح إلى Firestore\nمعرف المستخدم: {userUid}\nالوقت: {DateTime.Now:yyyy/MM/dd HH:mm:ss}", 
                        true);
                    
                    Debug.WriteLine($"✅ تم إرسال البيانات بنجاح للمستخدم: {userUid}");
                }
                else
                {
                    ShowResult("❌ فشل الإرسال", 
                        "فشل في إرسال البيانات إلى Firestore. تحقق من الاتصال والإعدادات.", 
                        false);
                    
                    Debug.WriteLine($"❌ فشل في إرسال البيانات للمستخدم: {userUid}");
                }
            }
            catch (Exception ex)
            {
                ShowResult("❌ خطأ في الإرسال", 
                    $"حدث خطأ أثناء إرسال البيانات: {ex.Message}", 
                    false);
                
                Debug.WriteLine($"❌ خطأ في إرسال البيانات: {ex.Message}");
            }
            finally
            {
                // إعادة تفعيل الزر
                SendDataButton.IsEnabled = true;
                SendDataButton.Content = "📤 إرسال بيانات إلى Firestore";
            }
        }

        /// <summary>
        /// عرض نتيجة العملية
        /// </summary>
        private void ShowResult(string title, string message, bool isSuccess)
        {
            ResultTitleTextBlock.Text = title;
            ResultMessageTextBlock.Text = message;
            
            if (isSuccess)
            {
                ResultPanel.Background = new System.Windows.Media.SolidColorBrush(
                    System.Windows.Media.Color.FromRgb(232, 245, 232)); // أخضر فاتح
                ResultPanel.BorderBrush = new System.Windows.Media.SolidColorBrush(
                    System.Windows.Media.Color.FromRgb(76, 175, 80)); // أخضر
                ResultTitleTextBlock.Foreground = new System.Windows.Media.SolidColorBrush(
                    System.Windows.Media.Color.FromRgb(76, 175, 80));
                ResultMessageTextBlock.Foreground = new System.Windows.Media.SolidColorBrush(
                    System.Windows.Media.Color.FromRgb(46, 125, 50));
            }
            else
            {
                ResultPanel.Background = new System.Windows.Media.SolidColorBrush(
                    System.Windows.Media.Color.FromRgb(255, 235, 238)); // أحمر فاتح
                ResultPanel.BorderBrush = new System.Windows.Media.SolidColorBrush(
                    System.Windows.Media.Color.FromRgb(244, 67, 54)); // أحمر
                ResultTitleTextBlock.Foreground = new System.Windows.Media.SolidColorBrush(
                    System.Windows.Media.Color.FromRgb(244, 67, 54));
                ResultMessageTextBlock.Foreground = new System.Windows.Media.SolidColorBrush(
                    System.Windows.Media.Color.FromRgb(198, 40, 40));
            }
            
            ResultPanel.Visibility = Visibility.Visible;
        }

        /// <summary>
        /// معالج حدث زر تسجيل الخروج
        /// </summary>
        private void LogoutButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // إظهار رسالة تأكيد
                var result = MessageBox.Show(
                    "هل أنت متأكد من تسجيل الخروج؟",
                    "تأكيد تسجيل الخروج",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    // تنظيف الموارد
                    timer?.Stop();
                    authLink?.Dispose();
                    
                    // فتح نافذة تسجيل الدخول
                    var loginWindow = new LoginWindow();
                    loginWindow.Show();
                    
                    // إغلاق النافذة الحالية
                    this.Close();
                    
                    Debug.WriteLine("✅ تم تسجيل الخروج بنجاح");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ خطأ في تسجيل الخروج: {ex.Message}");
                MessageBox.Show($"خطأ في تسجيل الخروج: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// معالج تغيير حالة اتصال Firestore
        /// </summary>
        private void OnFirestoreConnectionStatusChanged(bool isConnected, string message)
        {
            Dispatcher.Invoke(() =>
            {
                if (isConnected)
                {
                    StatusText.Text = "✅ متصل بـ Firestore ومصادق عليه";
                }
                else
                {
                    StatusText.Text = $"❌ خطأ في الاتصال: {message}";
                }
            });
        }

        /// <summary>
        /// معالج تحديث بيانات Firestore
        /// </summary>
        private void OnFirestoreDataUpdated(List<ContentItem> items)
        {
            Dispatcher.Invoke(() =>
            {
                Debug.WriteLine($"📊 تم تحديث البيانات - عدد العناصر: {items?.Count ?? 0}");
            });
        }

        /// <summary>
        /// معالج إغلاق النافذة
        /// </summary>
        protected override void OnClosed(EventArgs e)
        {
            // تنظيف الموارد
            timer?.Stop();
            authLink?.Dispose();
            
            // إنهاء التطبيق إذا كانت هذه النافذة الوحيدة المفتوحة
            if (Application.Current.Windows.Count == 1)
            {
                Application.Current.Shutdown();
            }
            
            base.OnClosed(e);
        }
    }
}
