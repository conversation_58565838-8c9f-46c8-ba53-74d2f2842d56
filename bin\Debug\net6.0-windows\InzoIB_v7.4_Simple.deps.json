{"runtimeTarget": {"name": ".NETCoreApp,Version=v6.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v6.0": {"InzoIB_v7.4_Simple/1.0.0": {"dependencies": {"Firebase.Auth": "1.0.0", "FirebaseAuthentication.net": "4.1.0", "Google.Apis.Auth": "1.68.0", "Google.Cloud.Firestore": "3.8.0", "Newtonsoft.Json": "13.0.3", "OpenAI": "2.0.0", "Telegram.Bot": "19.0.0"}, "runtime": {"InzoIB_v7.4_Simple.dll": {}}}, "Firebase.Auth/1.0.0": {"dependencies": {"Microsoft.NETCore.Portable.Compatibility": "1.0.1", "NETStandard.Library": "1.6.0", "Newtonsoft.Json": "13.0.3"}}, "FirebaseAuthentication.net/4.1.0": {"dependencies": {"Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/netstandard2.0/Firebase.Auth.dll": {"assemblyVersion": "4.1.0.0", "fileVersion": "4.1.0.0"}}}, "Google.Api.CommonProtos/2.15.0": {"dependencies": {"Google.Protobuf": "3.25.0"}, "runtime": {"lib/netstandard2.0/Google.Api.CommonProtos.dll": {"assemblyVersion": "2.15.0.0", "fileVersion": "2.15.0.0"}}}, "Google.Api.Gax/4.8.0": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "6.0.0", "Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/netstandard2.0/Google.Api.Gax.dll": {"assemblyVersion": "4.8.0.0", "fileVersion": "4.8.0.0"}}}, "Google.Api.Gax.Grpc/4.8.0": {"dependencies": {"Google.Api.CommonProtos": "2.15.0", "Google.Api.Gax": "4.8.0", "Google.Apis.Auth": "1.68.0", "Grpc.Auth": "2.60.0", "Grpc.Core.Api": "2.60.0", "Grpc.Net.Client": "2.60.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0"}, "runtime": {"lib/netstandard2.0/Google.Api.Gax.Grpc.dll": {"assemblyVersion": "4.8.0.0", "fileVersion": "4.8.0.0"}}}, "Google.Apis/1.68.0": {"dependencies": {"Google.Apis.Core": "1.68.0"}, "runtime": {"lib/net6.0/Google.Apis.dll": {"assemblyVersion": "1.68.0.0", "fileVersion": "1.68.0.0"}}}, "Google.Apis.Auth/1.68.0": {"dependencies": {"Google.Apis": "1.68.0", "Google.Apis.Core": "1.68.0", "System.Management": "7.0.2"}, "runtime": {"lib/net6.0/Google.Apis.Auth.dll": {"assemblyVersion": "1.68.0.0", "fileVersion": "1.68.0.0"}}}, "Google.Apis.Core/1.68.0": {"dependencies": {"Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/net6.0/Google.Apis.Core.dll": {"assemblyVersion": "1.68.0.0", "fileVersion": "1.68.0.0"}}}, "Google.Cloud.Firestore/3.8.0": {"dependencies": {"Google.Cloud.Firestore.V1": "3.8.0", "System.Collections.Immutable": "6.0.0", "System.Linq.Async": "6.0.1"}, "runtime": {"lib/netstandard2.0/Google.Cloud.Firestore.dll": {"assemblyVersion": "3.8.0.0", "fileVersion": "3.8.0.0"}}}, "Google.Cloud.Firestore.V1/3.8.0": {"dependencies": {"Google.Api.Gax.Grpc": "4.8.0", "Google.Cloud.Location": "2.3.0", "Google.LongRunning": "3.3.0"}, "runtime": {"lib/netstandard2.0/Google.Cloud.Firestore.V1.dll": {"assemblyVersion": "3.8.0.0", "fileVersion": "3.8.0.0"}}}, "Google.Cloud.Location/2.3.0": {"dependencies": {"Google.Api.Gax.Grpc": "4.8.0"}, "runtime": {"lib/netstandard2.0/Google.Cloud.Location.dll": {"assemblyVersion": "2.3.0.0", "fileVersion": "2.3.0.0"}}}, "Google.LongRunning/3.3.0": {"dependencies": {"Google.Api.Gax.Grpc": "4.8.0"}, "runtime": {"lib/netstandard2.0/Google.LongRunning.dll": {"assemblyVersion": "3.3.0.0", "fileVersion": "3.3.0.0"}}}, "Google.Protobuf/3.25.0": {"runtime": {"lib/net5.0/Google.Protobuf.dll": {"assemblyVersion": "3.25.0.0", "fileVersion": "3.25.0.0"}}}, "Grpc.Auth/2.60.0": {"dependencies": {"Google.Apis.Auth": "1.68.0", "Grpc.Core.Api": "2.60.0"}, "runtime": {"lib/netstandard2.0/Grpc.Auth.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.60.0.0"}}}, "Grpc.Core.Api/2.60.0": {"runtime": {"lib/netstandard2.1/Grpc.Core.Api.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.60.0.0"}}}, "Grpc.Net.Client/2.60.0": {"dependencies": {"Grpc.Net.Common": "2.60.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.0"}, "runtime": {"lib/net6.0/Grpc.Net.Client.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.60.0.0"}}}, "Grpc.Net.Common/2.60.0": {"dependencies": {"Grpc.Core.Api": "2.60.0"}, "runtime": {"lib/net6.0/Grpc.Net.Common.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.60.0.0"}}}, "Microsoft.Bcl.AsyncInterfaces/6.0.0": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.21.52210"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/6.0.0": {"runtime": {"lib/net6.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.21.52210"}}}, "Microsoft.Extensions.Logging.Abstractions/6.0.0": {"runtime": {"lib/net6.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.21.52210"}}}, "Microsoft.NETCore.Jit/1.0.2": {}, "Microsoft.NETCore.Platforms/1.0.1": {}, "Microsoft.NETCore.Portable.Compatibility/1.0.1": {"dependencies": {"Microsoft.NETCore.Runtime.CoreCLR": "1.0.2"}, "runtime": {"lib/netstandard1.0/System.ServiceModel.dll": {"assemblyVersion": "2.0.5.0", "fileVersion": "4.6.24214.0"}}}, "Microsoft.NETCore.Runtime.CoreCLR/1.0.2": {"dependencies": {"Microsoft.NETCore.Jit": "1.0.2", "Microsoft.NETCore.Windows.ApiSets": "1.0.1"}}, "Microsoft.NETCore.Targets/1.0.1": {}, "Microsoft.NETCore.Windows.ApiSets/1.0.1": {}, "Microsoft.Win32.Primitives/4.0.1": {"dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Runtime": "4.1.0"}}, "NETStandard.Library/1.6.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.Win32.Primitives": "4.0.1", "System.AppContext": "4.1.0", "System.Collections": "4.0.11", "System.Collections.Concurrent": "4.0.12", "System.Console": "4.0.0", "System.Diagnostics.Debug": "4.0.11", "System.Diagnostics.Tools": "4.0.1", "System.Diagnostics.Tracing": "4.1.0", "System.Globalization": "4.0.11", "System.Globalization.Calendars": "4.0.1", "System.IO": "4.1.0", "System.IO.Compression": "4.1.0", "System.IO.Compression.ZipFile": "4.0.1", "System.IO.FileSystem": "4.0.1", "System.IO.FileSystem.Primitives": "4.0.1", "System.Linq": "4.1.0", "System.Linq.Expressions": "4.1.0", "System.Net.Http": "4.1.0", "System.Net.Primitives": "4.0.11", "System.Net.Sockets": "4.1.0", "System.ObjectModel": "4.0.12", "System.Reflection": "4.1.0", "System.Reflection.Extensions": "4.0.1", "System.Reflection.Primitives": "4.0.1", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.Handles": "4.0.1", "System.Runtime.InteropServices": "4.1.0", "System.Runtime.InteropServices.RuntimeInformation": "4.0.0", "System.Runtime.Numerics": "4.0.1", "System.Security.Cryptography.Algorithms": "4.2.0", "System.Security.Cryptography.Encoding": "4.0.0", "System.Security.Cryptography.Primitives": "4.0.0", "System.Security.Cryptography.X509Certificates": "4.1.0", "System.Text.Encoding": "4.0.11", "System.Text.Encoding.Extensions": "4.0.11", "System.Text.RegularExpressions": "4.1.0", "System.Threading": "4.0.11", "System.Threading.Tasks": "4.0.11", "System.Threading.Timer": "4.0.1", "System.Xml.ReaderWriter": "4.0.11", "System.Xml.XDocument": "4.0.11"}}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "13.0.0.0", "fileVersion": "13.0.3.27908"}}}, "OpenAI/2.0.0": {"dependencies": {"System.ClientModel": "1.1.0", "System.Diagnostics.DiagnosticSource": "6.0.1"}, "runtime": {"lib/net6.0/OpenAI.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.0.0.0"}}}, "runtime.native.System/4.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1"}}, "runtime.native.System.IO.Compression/4.1.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1"}}, "runtime.native.System.Net.Http/4.0.1": {"dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1"}}, "runtime.native.System.Security.Cryptography/4.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1"}}, "System.AppContext/4.1.0": {"dependencies": {"System.Runtime": "4.1.0"}}, "System.Buffers/4.0.0": {"dependencies": {"System.Diagnostics.Debug": "4.0.11", "System.Diagnostics.Tracing": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Threading": "4.0.11"}}, "System.ClientModel/1.1.0": {"dependencies": {"System.Memory.Data": "1.0.2", "System.Text.Json": "6.0.9"}, "runtime": {"lib/net6.0/System.ClientModel.dll": {"assemblyVersion": "1.1.0.0", "fileVersion": "1.100.24.46703"}}}, "System.CodeDom/7.0.0": {"runtime": {"lib/net6.0/System.CodeDom.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.0.22.51805"}}}, "System.Collections/4.0.11": {"dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Runtime": "4.1.0"}}, "System.Collections.Concurrent/4.0.12": {"dependencies": {"System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Diagnostics.Tracing": "4.1.0", "System.Globalization": "4.0.11", "System.Reflection": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Threading": "4.0.11", "System.Threading.Tasks": "4.0.11"}}, "System.Collections.Immutable/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Console/4.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.IO": "4.1.0", "System.Runtime": "4.1.0", "System.Text.Encoding": "4.0.11"}}, "System.Diagnostics.Debug/4.0.11": {"dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Runtime": "4.1.0"}}, "System.Diagnostics.DiagnosticSource/6.0.1": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net6.0/System.Diagnostics.DiagnosticSource.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.1523.11507"}}}, "System.Diagnostics.Tools/4.0.1": {"dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Runtime": "4.1.0"}}, "System.Diagnostics.Tracing/4.1.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Runtime": "4.1.0"}}, "System.Globalization/4.0.11": {"dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Runtime": "4.1.0"}}, "System.Globalization.Calendars/4.0.1": {"dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Globalization": "4.0.11", "System.Runtime": "4.1.0"}}, "System.Globalization.Extensions/4.0.1": {"dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "System.Globalization": "4.0.11", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.InteropServices": "4.1.0"}}, "System.IO/4.1.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Runtime": "4.1.0", "System.Text.Encoding": "4.0.11", "System.Threading.Tasks": "4.0.11"}}, "System.IO.Compression/4.1.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.IO": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.Handles": "4.0.1", "System.Runtime.InteropServices": "4.1.0", "System.Text.Encoding": "4.0.11", "System.Threading": "4.0.11", "System.Threading.Tasks": "4.0.11", "runtime.native.System": "4.0.0", "runtime.native.System.IO.Compression": "4.1.0"}}, "System.IO.Compression.ZipFile/4.0.1": {"dependencies": {"System.Buffers": "4.0.0", "System.IO": "4.1.0", "System.IO.Compression": "4.1.0", "System.IO.FileSystem": "4.0.1", "System.IO.FileSystem.Primitives": "4.0.1", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Text.Encoding": "4.0.11"}}, "System.IO.FileSystem/4.0.1": {"dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.IO": "4.1.0", "System.IO.FileSystem.Primitives": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Handles": "4.0.1", "System.Text.Encoding": "4.0.11", "System.Threading.Tasks": "4.0.11"}}, "System.IO.FileSystem.Primitives/4.0.1": {"dependencies": {"System.Runtime": "4.1.0"}}, "System.Linq/4.1.0": {"dependencies": {"System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0"}}, "System.Linq.Async/6.0.1": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "6.0.0"}, "runtime": {"lib/net6.0/System.Linq.Async.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.1.35981"}}}, "System.Linq.Expressions/4.1.0": {"dependencies": {"System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Globalization": "4.0.11", "System.IO": "4.1.0", "System.Linq": "4.1.0", "System.ObjectModel": "4.0.12", "System.Reflection": "4.1.0", "System.Reflection.Emit": "4.0.1", "System.Reflection.Emit.ILGeneration": "4.0.1", "System.Reflection.Emit.Lightweight": "4.0.1", "System.Reflection.Extensions": "4.0.1", "System.Reflection.Primitives": "4.0.1", "System.Reflection.TypeExtensions": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Threading": "4.0.11"}}, "System.Management/7.0.2": {"dependencies": {"System.CodeDom": "7.0.0"}, "runtime": {"lib/net6.0/System.Management.dll": {"assemblyVersion": "7.0.0.2", "fileVersion": "7.0.723.27404"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Management.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "7.0.0.2", "fileVersion": "7.0.723.27404"}}}, "System.Memory.Data/1.0.2": {"dependencies": {"System.Text.Encodings.Web": "6.0.0", "System.Text.Json": "6.0.9"}, "runtime": {"lib/netstandard2.0/System.Memory.Data.dll": {"assemblyVersion": "1.0.2.0", "fileVersion": "1.0.221.20802"}}}, "System.Net.Http/4.1.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Diagnostics.DiagnosticSource": "6.0.1", "System.Diagnostics.Tracing": "4.1.0", "System.Globalization": "4.0.11", "System.Globalization.Extensions": "4.0.1", "System.IO": "4.1.0", "System.IO.FileSystem": "4.0.1", "System.Net.Primitives": "4.0.11", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.Handles": "4.0.1", "System.Runtime.InteropServices": "4.1.0", "System.Security.Cryptography.Algorithms": "4.2.0", "System.Security.Cryptography.Encoding": "4.0.0", "System.Security.Cryptography.OpenSsl": "4.0.0", "System.Security.Cryptography.Primitives": "4.0.0", "System.Security.Cryptography.X509Certificates": "4.1.0", "System.Text.Encoding": "4.0.11", "System.Threading": "4.0.11", "System.Threading.Tasks": "4.0.11", "runtime.native.System": "4.0.0", "runtime.native.System.Net.Http": "4.0.1", "runtime.native.System.Security.Cryptography": "4.0.0"}}, "System.Net.Primitives/4.0.11": {"dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Runtime": "4.1.0", "System.Runtime.Handles": "4.0.1"}}, "System.Net.Sockets/4.1.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.IO": "4.1.0", "System.Net.Primitives": "4.0.11", "System.Runtime": "4.1.0", "System.Threading.Tasks": "4.0.11"}}, "System.ObjectModel/4.0.12": {"dependencies": {"System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Threading": "4.0.11"}}, "System.Reflection/4.1.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.IO": "4.1.0", "System.Reflection.Primitives": "4.0.1", "System.Runtime": "4.1.0"}}, "System.Reflection.Emit/4.0.1": {"dependencies": {"System.IO": "4.1.0", "System.Reflection": "4.1.0", "System.Reflection.Emit.ILGeneration": "4.0.1", "System.Reflection.Primitives": "4.0.1", "System.Runtime": "4.1.0"}}, "System.Reflection.Emit.ILGeneration/4.0.1": {"dependencies": {"System.Reflection": "4.1.0", "System.Reflection.Primitives": "4.0.1", "System.Runtime": "4.1.0"}}, "System.Reflection.Emit.Lightweight/4.0.1": {"dependencies": {"System.Reflection": "4.1.0", "System.Reflection.Emit.ILGeneration": "4.0.1", "System.Reflection.Primitives": "4.0.1", "System.Runtime": "4.1.0"}}, "System.Reflection.Extensions/4.0.1": {"dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Reflection": "4.1.0", "System.Runtime": "4.1.0"}}, "System.Reflection.Primitives/4.0.1": {"dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Runtime": "4.1.0"}}, "System.Reflection.TypeExtensions/4.1.0": {"dependencies": {"System.Reflection": "4.1.0", "System.Runtime": "4.1.0"}}, "System.Resources.ResourceManager/4.0.1": {"dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Globalization": "4.0.11", "System.Reflection": "4.1.0", "System.Runtime": "4.1.0"}}, "System.Runtime/4.1.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1"}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {}, "System.Runtime.Extensions/4.1.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Runtime": "4.1.0"}}, "System.Runtime.Handles/4.0.1": {"dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Runtime": "4.1.0"}}, "System.Runtime.InteropServices/4.1.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Reflection": "4.1.0", "System.Reflection.Primitives": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Handles": "4.0.1"}}, "System.Runtime.InteropServices.RuntimeInformation/4.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "System.Reflection": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.InteropServices": "4.1.0", "System.Threading": "4.0.11", "runtime.native.System": "4.0.0"}}, "System.Runtime.Numerics/4.0.1": {"dependencies": {"System.Globalization": "4.0.11", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0"}}, "System.Security.Cryptography.Algorithms/4.2.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "System.Collections": "4.0.11", "System.IO": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.Handles": "4.0.1", "System.Runtime.InteropServices": "4.1.0", "System.Runtime.Numerics": "4.0.1", "System.Security.Cryptography.Encoding": "4.0.0", "System.Security.Cryptography.Primitives": "4.0.0", "System.Text.Encoding": "4.0.11", "runtime.native.System.Security.Cryptography": "4.0.0"}}, "System.Security.Cryptography.Cng/4.2.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "System.IO": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.Handles": "4.0.1", "System.Runtime.InteropServices": "4.1.0", "System.Security.Cryptography.Algorithms": "4.2.0", "System.Security.Cryptography.Encoding": "4.0.0", "System.Security.Cryptography.Primitives": "4.0.0", "System.Text.Encoding": "4.0.11"}}, "System.Security.Cryptography.Csp/4.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "System.IO": "4.1.0", "System.Reflection": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.Handles": "4.0.1", "System.Runtime.InteropServices": "4.1.0", "System.Security.Cryptography.Algorithms": "4.2.0", "System.Security.Cryptography.Encoding": "4.0.0", "System.Security.Cryptography.Primitives": "4.0.0", "System.Text.Encoding": "4.0.11", "System.Threading": "4.0.11"}}, "System.Security.Cryptography.Encoding/4.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "System.Collections": "4.0.11", "System.Collections.Concurrent": "4.0.12", "System.Linq": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.Handles": "4.0.1", "System.Runtime.InteropServices": "4.1.0", "System.Security.Cryptography.Primitives": "4.0.0", "System.Text.Encoding": "4.0.11", "runtime.native.System.Security.Cryptography": "4.0.0"}}, "System.Security.Cryptography.OpenSsl/4.0.0": {"dependencies": {"System.Collections": "4.0.11", "System.IO": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.Handles": "4.0.1", "System.Runtime.InteropServices": "4.1.0", "System.Runtime.Numerics": "4.0.1", "System.Security.Cryptography.Algorithms": "4.2.0", "System.Security.Cryptography.Encoding": "4.0.0", "System.Security.Cryptography.Primitives": "4.0.0", "System.Text.Encoding": "4.0.11", "runtime.native.System.Security.Cryptography": "4.0.0"}}, "System.Security.Cryptography.Primitives/4.0.0": {"dependencies": {"System.Diagnostics.Debug": "4.0.11", "System.Globalization": "4.0.11", "System.IO": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Threading": "4.0.11", "System.Threading.Tasks": "4.0.11"}}, "System.Security.Cryptography.X509Certificates/4.1.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Globalization": "4.0.11", "System.Globalization.Calendars": "4.0.1", "System.IO": "4.1.0", "System.IO.FileSystem": "4.0.1", "System.IO.FileSystem.Primitives": "4.0.1", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.Handles": "4.0.1", "System.Runtime.InteropServices": "4.1.0", "System.Runtime.Numerics": "4.0.1", "System.Security.Cryptography.Algorithms": "4.2.0", "System.Security.Cryptography.Cng": "4.2.0", "System.Security.Cryptography.Csp": "4.0.0", "System.Security.Cryptography.Encoding": "4.0.0", "System.Security.Cryptography.OpenSsl": "4.0.0", "System.Security.Cryptography.Primitives": "4.0.0", "System.Text.Encoding": "4.0.11", "System.Threading": "4.0.11", "runtime.native.System": "4.0.0", "runtime.native.System.Net.Http": "4.0.1", "runtime.native.System.Security.Cryptography": "4.0.0"}}, "System.Text.Encoding/4.0.11": {"dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Runtime": "4.1.0"}}, "System.Text.Encoding.Extensions/4.0.11": {"dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Runtime": "4.1.0", "System.Text.Encoding": "4.0.11"}}, "System.Text.Encodings.Web/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Text.Json/6.0.9": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Text.Encodings.Web": "6.0.0"}, "runtime": {"lib/net6.0/System.Text.Json.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.2523.51912"}}}, "System.Text.RegularExpressions/4.1.0": {"dependencies": {"System.Collections": "4.0.11", "System.Globalization": "4.0.11", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Threading": "4.0.11"}}, "System.Threading/4.0.11": {"dependencies": {"System.Runtime": "4.1.0", "System.Threading.Tasks": "4.0.11"}}, "System.Threading.Tasks/4.0.11": {"dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Runtime": "4.1.0"}}, "System.Threading.Tasks.Extensions/4.0.0": {"dependencies": {"System.Collections": "4.0.11", "System.Runtime": "4.1.0", "System.Threading.Tasks": "4.0.11"}}, "System.Threading.Timer/4.0.1": {"dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Runtime": "4.1.0"}}, "System.Xml.ReaderWriter/4.0.11": {"dependencies": {"System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Globalization": "4.0.11", "System.IO": "4.1.0", "System.IO.FileSystem": "4.0.1", "System.IO.FileSystem.Primitives": "4.0.1", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.InteropServices": "4.1.0", "System.Text.Encoding": "4.0.11", "System.Text.Encoding.Extensions": "4.0.11", "System.Text.RegularExpressions": "4.1.0", "System.Threading.Tasks": "4.0.11", "System.Threading.Tasks.Extensions": "4.0.0"}}, "System.Xml.XDocument/4.0.11": {"dependencies": {"System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Diagnostics.Tools": "4.0.1", "System.Globalization": "4.0.11", "System.IO": "4.1.0", "System.Reflection": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Text.Encoding": "4.0.11", "System.Threading": "4.0.11", "System.Xml.ReaderWriter": "4.0.11"}}, "Telegram.Bot/19.0.0": {"dependencies": {"Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/net6.0/Telegram.Bot.dll": {"assemblyVersion": "19.0.0.0", "fileVersion": "19.0.0.0"}}}}}, "libraries": {"InzoIB_v7.4_Simple/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Firebase.Auth/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-MRAGIRDXqB0jRvtJx0yRmTMjZInQcUSyHN7LuOwxL/nfyceDEA+Gfiimn7HtI2ntHw3hdUAoeeCA6BCc3qg0/A==", "path": "firebase.auth/1.0.0", "hashPath": "firebase.auth.1.0.0.nupkg.sha512"}, "FirebaseAuthentication.net/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-pShi5v9cvT5bUWZDm261lO4uxJHvzOvpj46YHrL3uCB1WBeyCIcpoVakAQ2xHTu1dpmV86/BzSNRpqIGNp860g==", "path": "firebaseauthentication.net/4.1.0", "hashPath": "firebaseauthentication.net.4.1.0.nupkg.sha512"}, "Google.Api.CommonProtos/2.15.0": {"type": "package", "serviceable": true, "sha512": "sha512-k+43CTEgMbT4C2JjcIJon3GvMNFJ3kLMjPTedOkXUAt0D5cFfrlee+M4oieueAVhbXktR89yoj5O/kVmC/AKJw==", "path": "google.api.commonprotos/2.15.0", "hashPath": "google.api.commonprotos.2.15.0.nupkg.sha512"}, "Google.Api.Gax/4.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-xlV8Jq/G5CQAA3PwYAuKGjfzGOP7AvjhREnE6vgZlzxREGYchHudZWa2PWSqFJL+MBtz9YgitLpRogANN3CVvg==", "path": "google.api.gax/4.8.0", "hashPath": "google.api.gax.4.8.0.nupkg.sha512"}, "Google.Api.Gax.Grpc/4.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-njc<PERSON>rFMZTmG2xdar6o5zyj7YVCUv6JNWU7un+q15Fm9DpGt0HSqHVnabVD2LBjUa3JWrqWX2kVv0w7qx4+tFQ==", "path": "google.api.gax.grpc/4.8.0", "hashPath": "google.api.gax.grpc.4.8.0.nupkg.sha512"}, "Google.Apis/1.68.0": {"type": "package", "serviceable": true, "sha512": "sha512-s2MymhdpH+ybZNBeZ2J5uFgFHApBp+QXf9FjZSdM1lk/vx5VqIknJwnaWiuAzXxPrLEkesX0Q+UsiWn39yZ9zw==", "path": "google.apis/1.68.0", "hashPath": "google.apis.1.68.0.nupkg.sha512"}, "Google.Apis.Auth/1.68.0": {"type": "package", "serviceable": true, "sha512": "sha512-hFx8Qz5bZ4w0hpnn4tSmZaaFpjAMsgVElZ+ZgVLUZ2r9i+AKcoVgwiNfv1pruNS5cCvpXqhKECbruBCfRezPHA==", "path": "google.apis.auth/1.68.0", "hashPath": "google.apis.auth.1.68.0.nupkg.sha512"}, "Google.Apis.Core/1.68.0": {"type": "package", "serviceable": true, "sha512": "sha512-pAqwa6pfu53UXCR2b7A/PAPXeuVg6L1OFw38WckN27NU2+mf+KTjoEg2YGv/f0UyKxzz7DxF1urOTKg/6dTP9g==", "path": "google.apis.core/1.68.0", "hashPath": "google.apis.core.1.68.0.nupkg.sha512"}, "Google.Cloud.Firestore/3.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-22Isdmp48+XzOOI3pmmd3dLgJT5JKCUIw0eMqoQfZL96spLc3SlNTeC7WerAsDhvii73S0VLsLXb5KfEmcTA/Q==", "path": "google.cloud.firestore/3.8.0", "hashPath": "google.cloud.firestore.3.8.0.nupkg.sha512"}, "Google.Cloud.Firestore.V1/3.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-wBO8OtxkP7y4CnKjUMJo2YyKoGWbN6UkmWN+Rm9p2vqvUwgHPIT5OZVlJBxPyFDWLUuGSBYVBoeathsZfjt/gg==", "path": "google.cloud.firestore.v1/3.8.0", "hashPath": "google.cloud.firestore.v1.3.8.0.nupkg.sha512"}, "Google.Cloud.Location/2.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ABQ4EM7FsOM7tx0cmlkZmHFqH1LeCf4teWPM26UT7mZJzlH4Pk8HUcyi/xEFe3l6LanNFCTHbKT+eOlQ/axkJg==", "path": "google.cloud.location/2.3.0", "hashPath": "google.cloud.location.2.3.0.nupkg.sha512"}, "Google.LongRunning/3.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-F2SZ83Jo466Wj/s1Z7QhIAmWBXxJZQyXZpcx0P8BR7d6s0FAj67vQjeUPESSJcvsy8AqYiYBhkUr2YpZhTQeHg==", "path": "google.longrunning/3.3.0", "hashPath": "google.longrunning.3.3.0.nupkg.sha512"}, "Google.Protobuf/3.25.0": {"type": "package", "serviceable": true, "sha512": "sha512-pIEkH1IqZV1iK8J5MYdG1kOyY0EoQLB6yEKvBq12RYNtvGXwCvnQg5zQsFmcqAEPtIZvSqPozIbUZaEd5a2gCg==", "path": "google.protobuf/3.25.0", "hashPath": "google.protobuf.3.25.0.nupkg.sha512"}, "Grpc.Auth/2.60.0": {"type": "package", "serviceable": true, "sha512": "sha512-mJAllsSsFix/wrp1l5QmHXRlzMFmnIGZJYuHG1thkDJPUE9ltHPkdL5x30xGpqKvka02Il2oHd66gt8YfSOIWg==", "path": "grpc.auth/2.60.0", "hashPath": "grpc.auth.2.60.0.nupkg.sha512"}, "Grpc.Core.Api/2.60.0": {"type": "package", "serviceable": true, "sha512": "sha512-VWah+8dGJhhsay5BQ/Ljq6GYDWj0lSjdzqyoBgUQhXTbBqhs+q5dRFROKxI1xxzlL4pfUO45cf/y+KnHVFG9ew==", "path": "grpc.core.api/2.60.0", "hashPath": "grpc.core.api.2.60.0.nupkg.sha512"}, "Grpc.Net.Client/2.60.0": {"type": "package", "serviceable": true, "sha512": "sha512-J9U96gjZHOcqSgAThg9vZZhLsbTD005bUggPtMP/RVQnGc3+tQJTpkRUCJtJWq9cykNydsRVoyU38TjPP/VJ4A==", "path": "grpc.net.client/2.60.0", "hashPath": "grpc.net.client.2.60.0.nupkg.sha512"}, "Grpc.Net.Common/2.60.0": {"type": "package", "serviceable": true, "sha512": "sha512-Y/917aplgD1RA0q1cd9WpnMGyl9Luu3WZl6ZMpPvNQwg2TNw/3uXUDSriDBybeCtxnKUCtxUcWO3WsVkhM1DcA==", "path": "grpc.net.common/2.60.0", "hashPath": "grpc.net.common.2.60.0.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-UcSjPsst+DfAdJGVDsu346FX0ci0ah+lw3WRtn18NUwEqRt70HaOQ7lI72vy3+1LxtqI3T5GWwV39rQSrCzAeg==", "path": "microsoft.bcl.asyncinterfaces/6.0.0", "hashPath": "microsoft.bcl.asyncinterfaces.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-xlzi2IYREJH3/m6+lUrQlujzX8wDitm4QGnUu6kUXTQAWPuZY8i+ticFJbzfqaetLA6KR/rO6Ew/HuYD+bxifg==", "path": "microsoft.extensions.dependencyinjection.abstractions/6.0.0", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/HggWBbTwy8TgebGSX5DBZ24ndhzi93sHUBDvP1IxbZD7FDokYzdAr6+vbWGjw2XAfR2EJ1sfKUotpjHnFWPxA==", "path": "microsoft.extensions.logging.abstractions/6.0.0", "hashPath": "microsoft.extensions.logging.abstractions.6.0.0.nupkg.sha512"}, "Microsoft.NETCore.Jit/1.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-Ok2vWofa6X8WD9vc4pfLHwvJz1/B6t3gOAoZcjrjrQf7lQOlNIuZIZtLn3wnWX28DuQGpPJkRlBxFj7Z5txNqw==", "path": "microsoft.netcore.jit/1.0.2", "hashPath": "microsoft.netcore.jit.1.0.2.nupkg.sha512"}, "Microsoft.NETCore.Platforms/1.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-2G6OjjJzwBfNOO8myRV/nFrbTw5iA+DEm0N+qUqhrOmaVtn4pC77h38I1jsXGw5VH55+dPfQsqHD0We9sCl9FQ==", "path": "microsoft.netcore.platforms/1.0.1", "hashPath": "microsoft.netcore.platforms.1.0.1.nupkg.sha512"}, "Microsoft.NETCore.Portable.Compatibility/1.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-Vd+lvLcGwvkedxtKn0U8s9uR4p0Lm+0U2QvDsLaw7g4S1W4KfPDbaW+ROhhLCSOx/gMYC72/b+z+o4fqS/oxVg==", "path": "microsoft.netcore.portable.compatibility/1.0.1", "hashPath": "microsoft.netcore.portable.compatibility.1.0.1.nupkg.sha512"}, "Microsoft.NETCore.Runtime.CoreCLR/1.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-A0x1xtTjYJWZr2DRzgfCOXgB0JkQg8twnmtTJ79wFje+IihlLbXtx6Z2AxyVokBM5ruwTedR6YdCmHk39QJdtQ==", "path": "microsoft.netcore.runtime.coreclr/1.0.2", "hashPath": "microsoft.netcore.runtime.coreclr.1.0.2.nupkg.sha512"}, "Microsoft.NETCore.Targets/1.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-rkn+fKobF/cbWfnnfBOQHKVKIOpxMZBvlSHkqDWgBpwGDcLRduvs3D9OLGeV6GWGvVwNlVi2CBbTjuPmtHvyNw==", "path": "microsoft.netcore.targets/1.0.1", "hashPath": "microsoft.netcore.targets.1.0.1.nupkg.sha512"}, "Microsoft.NETCore.Windows.ApiSets/1.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-SaToCvvsGMxTgtLv/BrFQ5IFMPRE1zpWbnqbpwykJa8W5XiX82CXI6K2o7yf5xS7EP6t/JzFLV0SIDuWpvBZVw==", "path": "microsoft.netcore.windows.apisets/1.0.1", "hashPath": "microsoft.netcore.windows.apisets.1.0.1.nupkg.sha512"}, "Microsoft.Win32.Primitives/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-fQnBHO9DgcmkC9dYSJoBqo6sH1VJwJprUHh8F3hbcRlxiQiBUuTntdk8tUwV490OqC2kQUrinGwZyQHTieuXRA==", "path": "microsoft.win32.primitives/4.0.1", "hashPath": "microsoft.win32.primitives.4.0.1.nupkg.sha512"}, "NETStandard.Library/1.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-ypsCvIdCZ4IoYASJHt6tF2fMo7N30NLgV1EbmC+snO490OMl9FvVxmumw14rhReWU3j3g7BYudG6YCrchwHJlA==", "path": "netstandard.library/1.6.0", "hashPath": "netstandard.library.1.6.0.nupkg.sha512"}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}, "OpenAI/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-WI9e5tC15ZBt02scCp2gcHrjxQRVX2Ws3eUu/YsICqellH3MwdRggGujSZg69oBJMtk1AYmqb9l08ix0l5AcqQ==", "path": "openai/2.0.0", "hashPath": "openai.2.0.0.nupkg.sha512"}, "runtime.native.System/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-QfS/nQI7k/BLgmLrw7qm7YBoULEvgWnPI+cYsbfCVFTW8Aj+i8JhccxcFMu1RWms0YZzF+UHguNBK4Qn89e2Sg==", "path": "runtime.native.system/4.0.0", "hashPath": "runtime.native.system.4.0.0.nupkg.sha512"}, "runtime.native.System.IO.Compression/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-Ob7nvnJBox1aaB222zSVZSkf4WrebPG4qFscfK7vmD7P7NxoSxACQLtO7ytWpqXDn2wcd/+45+EAZ7xjaPip8A==", "path": "runtime.native.system.io.compression/4.1.0", "hashPath": "runtime.native.system.io.compression.4.1.0.nupkg.sha512"}, "runtime.native.System.Net.Http/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-Nh0UPZx2Vifh8r+J+H2jxifZUD3sBrmolgiFWJd2yiNrxO0xTa6bAw3YwRn1VOiSen/tUXMS31ttNItCZ6lKuA==", "path": "runtime.native.system.net.http/4.0.1", "hashPath": "runtime.native.system.net.http.4.0.1.nupkg.sha512"}, "runtime.native.System.Security.Cryptography/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-2CQK0jmO6Eu7ZeMgD+LOFbNJSXHFVQbCJJkEyEwowh1SCgYnrn9W9RykMfpeeVGw7h4IBvYikzpGUlmZTUafJw==", "path": "runtime.native.system.security.cryptography/4.0.0", "hashPath": "runtime.native.system.security.cryptography.4.0.0.nupkg.sha512"}, "System.AppContext/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-3QjO4jNV7PdKkmQAVp9atA+usVnKRwI3Kx1nMwJ93T0LcQfx7pKAYk0nKz5wn1oP5iqlhZuy6RXOFdhr7rDwow==", "path": "system.appcontext/4.1.0", "hashPath": "system.appcontext.4.1.0.nupkg.sha512"}, "System.Buffers/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-msXumHfjjURSkvxUjYuq4N2ghHoRi2VpXcKMA7gK6ujQfU3vGpl+B6ld0ATRg+FZFpRyA6PgEPA+VlIkTeNf2w==", "path": "system.buffers/4.0.0", "hashPath": "system.buffers.4.0.0.nupkg.sha512"}, "System.ClientModel/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-UocOlCkxLZrG2CKMAAImPcldJTxeesHnHGHwhJ0pNlZEvEXcWKuQvVOER2/NiOkJGRJk978SNdw3j6/7O9H1lg==", "path": "system.clientmodel/1.1.0", "hashPath": "system.clientmodel.1.1.0.nupkg.sha512"}, "System.CodeDom/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-GLltyqEsE5/3IE+zYRP5sNa1l44qKl9v+bfdMcwg+M9qnQf47wK3H0SUR/T+3N4JEQXF3vV4CSuuo0rsg+nq2A==", "path": "system.codedom/7.0.0", "hashPath": "system.codedom.7.0.0.nupkg.sha512"}, "System.Collections/4.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-YUJGz6eFKqS0V//mLt25vFGrrCvOnsXjlvFQs+KimpwNxug9x0Pzy4PlFMU3Q2IzqAa9G2L4LsK3+9vCBK7oTg==", "path": "system.collections/4.0.11", "hashPath": "system.collections.4.0.11.nupkg.sha512"}, "System.Collections.Concurrent/4.0.12": {"type": "package", "serviceable": true, "sha512": "sha512-2gBcbb3drMLgxlI0fBfxMA31ec6AEyYCHygGse4vxceJan8mRIWeKJ24BFzN7+bi/NFTgdIgufzb94LWO5EERQ==", "path": "system.collections.concurrent/4.0.12", "hashPath": "system.collections.concurrent.4.0.12.nupkg.sha512"}, "System.Collections.Immutable/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-l4zZJ1WU2hqpQQHXz1rvC3etVZN+2DLmQMO79FhOTZHMn8tDRr+WU287sbomD0BETlmKDn0ygUgVy9k5xkkJdA==", "path": "system.collections.immutable/6.0.0", "hashPath": "system.collections.immutable.6.0.0.nupkg.sha512"}, "System.Console/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-qSKUSOIiYA/a0g5XXdxFcUFmv1hNICBD7QZ0QhGYVipPIhvpiydY8VZqr1thmCXvmn8aipMg64zuanB4eotK9A==", "path": "system.console/4.0.0", "hashPath": "system.console.4.0.0.nupkg.sha512"}, "System.Diagnostics.Debug/4.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-w5U95fVKHY4G8ASs/K5iK3J5LY+/dLFd4vKejsnI/ZhBsWS9hQakfx3Zr7lRWKg4tAw9r4iktyvsTagWkqYCiw==", "path": "system.diagnostics.debug/4.0.11", "hashPath": "system.diagnostics.debug.4.0.11.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-KiLYDu2k2J82Q9BJpWiuQqCkFjRBWVq4jDzKKWawVi9KWzyD0XG3cmfX0vqTQlL14Wi9EufJrbL0+KCLTbqWiQ==", "path": "system.diagnostics.diagnosticsource/6.0.1", "hashPath": "system.diagnostics.diagnosticsource.6.0.1.nupkg.sha512"}, "System.Diagnostics.Tools/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-xBfJ8pnd4C17dWaC9FM6aShzbJcRNMChUMD42I6772KGGrqaFdumwhn9OdM68erj1ueNo3xdQ1EwiFjK5k8p0g==", "path": "system.diagnostics.tools/4.0.1", "hashPath": "system.diagnostics.tools.4.0.1.nupkg.sha512"}, "System.Diagnostics.Tracing/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-vDN1PoMZCkkdNjvZLql592oYJZgS7URcJzJ7bxeBgGtx5UtR5leNm49VmfHGqIffX4FKacHbI3H6UyNSHQknBg==", "path": "system.diagnostics.tracing/4.1.0", "hashPath": "system.diagnostics.tracing.4.1.0.nupkg.sha512"}, "System.Globalization/4.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-B95h0YLEL2oSnwF/XjqSWKnwKOy/01VWkNlsCeMTFJLLabflpGV26nK164eRs5GiaRSBGpOxQ3pKoSnnyZN5pg==", "path": "system.globalization/4.0.11", "hashPath": "system.globalization.4.0.11.nupkg.sha512"}, "System.Globalization.Calendars/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-L1c6IqeQ88vuzC1P81JeHmHA8mxq8a18NUBNXnIY/BVb+TCyAaGIFbhpZt60h9FJNmisymoQkHEFSE9Vslja1Q==", "path": "system.globalization.calendars/4.0.1", "hashPath": "system.globalization.calendars.4.0.1.nupkg.sha512"}, "System.Globalization.Extensions/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-KKo23iKeOaIg61SSXwjANN7QYDr/3op3OWGGzDzz7mypx0Za0fZSeG0l6cco8Ntp8YMYkIQcAqlk8yhm5/Uhcg==", "path": "system.globalization.extensions/4.0.1", "hashPath": "system.globalization.extensions.4.0.1.nupkg.sha512"}, "System.IO/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-3KlTJceQc3gnGIaHZ7UBZO26SHL1SHE4ddrmiwumFnId+CEHP+O8r386tZKaE6zlk5/mF8vifMBzHj9SaXN+mQ==", "path": "system.io/4.1.0", "hashPath": "system.io.4.1.0.nupkg.sha512"}, "System.IO.Compression/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-TjnBS6eztThSzeSib+WyVbLzEdLKUcEHN69VtS3u8aAsSc18FU6xCZlNWWsEd8SKcXAE+y1sOu7VbU8sUeM0sg==", "path": "system.io.compression/4.1.0", "hashPath": "system.io.compression.4.1.0.nupkg.sha512"}, "System.IO.Compression.ZipFile/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-hBQYJzfTbQURF10nLhd+az2NHxsU6MU7AB8RUf4IolBP5lOAm4Luho851xl+CqslmhI5ZH/el8BlngEk4lBkaQ==", "path": "system.io.compression.zipfile/4.0.1", "hashPath": "system.io.compression.zipfile.4.0.1.nupkg.sha512"}, "System.IO.FileSystem/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-IBErlVq5jOggAD69bg1t0pJcHaDbJbWNUZTPI96fkYWzwYbN6D9wRHMULLDd9dHsl7C2YsxXL31LMfPI1SWt8w==", "path": "system.io.filesystem/4.0.1", "hashPath": "system.io.filesystem.4.0.1.nupkg.sha512"}, "System.IO.FileSystem.Primitives/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-kWkKD203JJKxJeE74p8aF8y4Qc9r9WQx4C0cHzHPrY3fv/L/IhWnyCHaFJ3H1QPOH6A93whlQ2vG5nHlBDvzWQ==", "path": "system.io.filesystem.primitives/4.0.1", "hashPath": "system.io.filesystem.primitives.4.0.1.nupkg.sha512"}, "System.Linq/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-bQ0iYFOQI0nuTnt+NQADns6ucV4DUvMdwN6CbkB1yj8i7arTGiTN5eok1kQwdnnNWSDZfIUySQY+J3d5KjWn0g==", "path": "system.linq/4.1.0", "hashPath": "system.linq.4.1.0.nupkg.sha512"}, "System.Linq.Async/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-0YhHcaroWpQ9UCot3Pizah7ryAzQhNvobLMSxeDIGmnXfkQn8u5owvpOH0K6EVB+z9L7u6Cc4W17Br/+jyttEQ==", "path": "system.linq.async/6.0.1", "hashPath": "system.linq.async.6.0.1.nupkg.sha512"}, "System.Linq.Expressions/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-I+y02iqkgmCAyfbqOmSDOgqdZQ5tTj80Akm5BPSS8EeB0VGWdy6X1KCoYe8Pk6pwDoAKZUOdLVxnTJcExiv5zw==", "path": "system.linq.expressions/4.1.0", "hashPath": "system.linq.expressions.4.1.0.nupkg.sha512"}, "System.Management/7.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-/qEUN91mP/MUQmJnM5y5BdT7ZoPuVrtxnFlbJ8a3kBJGhe2wCzBfnPFtK2wTtEEcf3DMGR9J00GZZfg6HRI6yA==", "path": "system.management/7.0.2", "hashPath": "system.management.7.0.2.nupkg.sha512"}, "System.Memory.Data/1.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-JGkzeqgBsiZwKJZ1IxPNsDFZDhUvuEdX8L8BDC8N3KOj+6zMcNU28CNN59TpZE/VJYy9cP+5M+sbxtWJx3/xtw==", "path": "system.memory.data/1.0.2", "hashPath": "system.memory.data.1.0.2.nupkg.sha512"}, "System.Net.Http/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-ULq9g3SOPVuupt+Y3U+A37coXzdNisB1neFCSKzBwo182u0RDddKJF8I5+HfyXqK6OhJPgeoAwWXrbiUXuRDsg==", "path": "system.net.http/4.1.0", "hashPath": "system.net.http.4.1.0.nupkg.sha512"}, "System.Net.Primitives/4.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-hVvfl4405DRjA2408luZekbPhplJK03j2Y2lSfMlny7GHXlkByw1iLnc9mgKW0GdQn73vvMcWrWewAhylXA4Nw==", "path": "system.net.primitives/4.0.11", "hashPath": "system.net.primitives.4.0.11.nupkg.sha512"}, "System.Net.Sockets/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-xAz0N3dAV/aR/9g8r0Y5oEqU1JRsz29F5EGb/WVHmX3jVSLqi2/92M5hTad2aNWovruXrJpJtgZ9fccPMG9uSw==", "path": "system.net.sockets/4.1.0", "hashPath": "system.net.sockets.4.1.0.nupkg.sha512"}, "System.ObjectModel/4.0.12": {"type": "package", "serviceable": true, "sha512": "sha512-tAgJM1xt3ytyMoW4qn4wIqgJYm7L7TShRZG4+Q4Qsi2PCcj96pXN7nRywS9KkB3p/xDUjc2HSwP9SROyPYDYKQ==", "path": "system.objectmodel/4.0.12", "hashPath": "system.objectmodel.4.0.12.nupkg.sha512"}, "System.Reflection/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-JCKANJ0TI7kzoQzuwB/OoJANy1Lg338B6+JVacPl4TpUwi3cReg3nMLplMq2uqYfHFQpKIlHAUVAJlImZz/4ng==", "path": "system.reflection/4.1.0", "hashPath": "system.reflection.4.1.0.nupkg.sha512"}, "System.Reflection.Emit/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-P2wqAj72fFjpP6wb9nSfDqNBMab+2ovzSDzUZK7MVIm54tBJEPr9jWfSjjoTpPwj1LeKcmX3vr0ttyjSSFM47g==", "path": "system.reflection.emit/4.0.1", "hashPath": "system.reflection.emit.4.0.1.nupkg.sha512"}, "System.Reflection.Emit.ILGeneration/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-Ov6dU8Bu15Bc7zuqttgHF12J5lwSWyTf1S+FJouUXVMSqImLZzYaQ+vRr1rQ0OZ0HqsrwWl4dsKHELckQkVpgA==", "path": "system.reflection.emit.ilgeneration/4.0.1", "hashPath": "system.reflection.emit.ilgeneration.4.0.1.nupkg.sha512"}, "System.Reflection.Emit.Lightweight/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-sSzHHXueZ5Uh0OLpUQprhr+ZYJrLPA2Cmr4gn0wj9+FftNKXx8RIMKvO9qnjk2ebPYUjZ+F2ulGdPOsvj+MEjA==", "path": "system.reflection.emit.lightweight/4.0.1", "hashPath": "system.reflection.emit.lightweight.4.0.1.nupkg.sha512"}, "System.Reflection.Extensions/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-GYrtRsZcMuHF3sbmRHfMYpvxZoIN2bQGrYGerUiWLEkqdEUQZhH3TRSaC/oI4wO0II1RKBPlpIa1TOMxIcOOzQ==", "path": "system.reflection.extensions/4.0.1", "hashPath": "system.reflection.extensions.4.0.1.nupkg.sha512"}, "System.Reflection.Primitives/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-4inTox4wTBaDhB7V3mPvp9XlCbeGYWVEM9/fXALd52vNEAVisc1BoVWQPuUuD0Ga//dNbA/WeMy9u9mzLxGTHQ==", "path": "system.reflection.primitives/4.0.1", "hashPath": "system.reflection.primitives.4.0.1.nupkg.sha512"}, "System.Reflection.TypeExtensions/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-tsQ/ptQ3H5FYfON8lL4MxRk/8kFyE0A+tGPXmVP967cT/gzLHYxIejIYSxp4JmIeFHVP78g/F2FE1mUUTbDtrg==", "path": "system.reflection.typeextensions/4.1.0", "hashPath": "system.reflection.typeextensions.4.1.0.nupkg.sha512"}, "System.Resources.ResourceManager/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-TxwVeUNoTgUOdQ09gfTjvW411MF+w9MBYL7AtNVc+HtBCFlutPLhUCdZjNkjbhj3bNQWMdHboF0KIWEOjJssbA==", "path": "system.resources.resourcemanager/4.0.1", "hashPath": "system.resources.resourcemanager.4.0.1.nupkg.sha512"}, "System.Runtime/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-v6c/4Yaa9uWsq+JMhnOFewrYkgdNHNG2eMKuNqRn8P733rNXeRCGvV5FkkjBXn2dbVkPXOsO0xjsEeM1q2zC0g==", "path": "system.runtime/4.1.0", "hashPath": "system.runtime.4.1.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "path": "system.runtime.compilerservices.unsafe/6.0.0", "hashPath": "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"}, "System.Runtime.Extensions/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-CUOHjTT/vgP0qGW22U4/hDlOqXmcPq5YicBaXdUR2UiUoLwBT+olO6we4DVbq57jeX5uXH2uerVZhf0qGj+sVQ==", "path": "system.runtime.extensions/4.1.0", "hashPath": "system.runtime.extensions.4.1.0.nupkg.sha512"}, "System.Runtime.Handles/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-nCJvEKguXEvk2ymk1gqj625vVnlK3/xdGzx0vOKicQkoquaTBJTP13AIYkocSUwHCLNBwUbXTqTWGDxBTWpt7g==", "path": "system.runtime.handles/4.0.1", "hashPath": "system.runtime.handles.4.0.1.nupkg.sha512"}, "System.Runtime.InteropServices/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-16eu3kjHS633yYdkjwShDHZLRNMKVi/s0bY8ODiqJ2RfMhDMAwxZaUaWVnZ2P71kr/or+X9o/xFWtNqz8ivieQ==", "path": "system.runtime.interopservices/4.1.0", "hashPath": "system.runtime.interopservices.4.1.0.nupkg.sha512"}, "System.Runtime.InteropServices.RuntimeInformation/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-hWPhJxc453RCa8Z29O91EmfGeZIHX1ZH2A8L6lYQVSaKzku2DfArSfMEb1/MYYzPQRJZeu0c9dmYeJKxW5Fgng==", "path": "system.runtime.interopservices.runtimeinformation/4.0.0", "hashPath": "system.runtime.interopservices.runtimeinformation.4.0.0.nupkg.sha512"}, "System.Runtime.Numerics/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-+XbKFuzdmLP3d1o9pdHu2nxjNr2OEPqGzKeegPLCUMM71a0t50A/rOcIRmGs9wR7a8KuHX6hYs/7/TymIGLNqg==", "path": "system.runtime.numerics/4.0.1", "hashPath": "system.runtime.numerics.4.0.1.nupkg.sha512"}, "System.Security.Cryptography.Algorithms/4.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-8JQFxbLVdrtIOKMDN38Fn0GWnqYZw/oMlwOUG/qz1jqChvyZlnUmu+0s7wLx7JYua/nAXoESpHA3iw11QFWhXg==", "path": "system.security.cryptography.algorithms/4.2.0", "hashPath": "system.security.cryptography.algorithms.4.2.0.nupkg.sha512"}, "System.Security.Cryptography.Cng/4.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-cUJ2h+ZvONDe28Szw3st5dOHdjndhJzQ2WObDEXAWRPEQBtVItVoxbXM/OEsTthl3cNn2dk2k0I3y45igCQcLw==", "path": "system.security.cryptography.cng/4.2.0", "hashPath": "system.security.cryptography.cng.4.2.0.nupkg.sha512"}, "System.Security.Cryptography.Csp/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/i1Usuo4PgAqgbPNC0NjbO3jPW//BoBlTpcWFD1EHVbidH21y4c1ap5bbEMSGAXjAShhMH4abi/K8fILrnu4BQ==", "path": "system.security.cryptography.csp/4.0.0", "hashPath": "system.security.cryptography.csp.4.0.0.nupkg.sha512"}, "System.Security.Cryptography.Encoding/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-FbKgE5MbxSQMPcSVRgwM6bXN3GtyAh04NkV8E5zKCBE26X0vYW0UtTa2FIgkH33WVqBVxRgxljlVYumWtU+HcQ==", "path": "system.security.cryptography.encoding/4.0.0", "hashPath": "system.security.cryptography.encoding.4.0.0.nupkg.sha512"}, "System.Security.Cryptography.OpenSsl/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-HUG/zNUJwEiLkoURDixzkzZdB5yGA5pQhDP93ArOpDPQMteURIGERRNzzoJlmTreLBWr5lkFSjjMSk8ySEpQMw==", "path": "system.security.cryptography.openssl/4.0.0", "hashPath": "system.security.cryptography.openssl.4.0.0.nupkg.sha512"}, "System.Security.Cryptography.Primitives/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Wkd7QryWYjkQclX0bngpntW5HSlMzeJU24UaLJQ7YTfI8ydAVAaU2J+HXLLABOVJlKTVvAeL0Aj39VeTe7L+oA==", "path": "system.security.cryptography.primitives/4.0.0", "hashPath": "system.security.cryptography.primitives.4.0.0.nupkg.sha512"}, "System.Security.Cryptography.X509Certificates/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-4HEfsQIKAhA1+ApNn729Gi09zh+lYWwyIuViihoMDWp1vQnEkL2ct7mAbhBlLYm+x/L4Rr/pyGge1lIY635e0w==", "path": "system.security.cryptography.x509certificates/4.1.0", "hashPath": "system.security.cryptography.x509certificates.4.1.0.nupkg.sha512"}, "System.Text.Encoding/4.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-U3gGeMlDZXxCEiY4DwVLSacg+DFWCvoiX+JThA/rvw37Sqrku7sEFeVBBBMBnfB6FeZHsyDx85HlKL19x0HtZA==", "path": "system.text.encoding/4.0.11", "hashPath": "system.text.encoding.4.0.11.nupkg.sha512"}, "System.Text.Encoding.Extensions/4.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-jtbiTDtvfLYgXn8PTfWI+SiBs51rrmO4AAckx4KR6vFK9Wzf6tI8kcRdsYQNwriUeQ1+CtQbM1W4cMbLXnj/OQ==", "path": "system.text.encoding.extensions/4.0.11", "hashPath": "system.text.encoding.extensions.4.0.11.nupkg.sha512"}, "System.Text.Encodings.Web/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Vg8eB5Tawm1IFqj4TVK1czJX89rhFxJo9ELqc/Eiq0eXy13RK00eubyU6TJE6y+GQXjyV5gSfiewDUZjQgSE0w==", "path": "system.text.encodings.web/6.0.0", "hashPath": "system.text.encodings.web.6.0.0.nupkg.sha512"}, "System.Text.Json/6.0.9": {"type": "package", "serviceable": true, "sha512": "sha512-2j16oUgtIzl7Xtk7demG0i/v5aU/ZvULcAnJvPb63U3ZhXJ494UYcxuEj5Fs49i3XDrk5kU/8I+6l9zRCw3cJw==", "path": "system.text.json/6.0.9", "hashPath": "system.text.json.6.0.9.nupkg.sha512"}, "System.Text.RegularExpressions/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-i88YCXpRTjCnoSQZtdlHkAOx4KNNik4hMy83n0+Ftlb7jvV6ZiZWMpnEZHhjBp6hQVh8gWd/iKNPzlPF7iyA2g==", "path": "system.text.regularexpressions/4.1.0", "hashPath": "system.text.regularexpressions.4.1.0.nupkg.sha512"}, "System.Threading/4.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-N+3xqIcg3VDKyjwwCGaZ9HawG9aC6cSDI+s7ROma310GQo8vilFZa86hqKppwTHleR/G0sfOzhvgnUxWCR/DrQ==", "path": "system.threading/4.0.11", "hashPath": "system.threading.4.0.11.nupkg.sha512"}, "System.Threading.Tasks/4.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-k1S4Gc6IGwtHGT8188RSeGaX86Qw/wnrgNLshJvsdNUOPP9etMmo8S07c+UlOAx4K/xLuN9ivA1bD0LVurtIxQ==", "path": "system.threading.tasks/4.0.11", "hashPath": "system.threading.tasks.4.0.11.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-pH4FZDsZQ/WmgJtN4LWYmRdJAEeVkyriSwrv2Teoe5FOU0Yxlb6II6GL8dBPOfRmutHGATduj3ooMt7dJ2+i+w==", "path": "system.threading.tasks.extensions/4.0.0", "hashPath": "system.threading.tasks.extensions.4.0.0.nupkg.sha512"}, "System.Threading.Timer/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-saGfUV8uqVW6LeURiqxcGhZ24PzuRNaUBtbhVeuUAvky1naH395A/1nY0P2bWvrw/BreRtIB/EzTDkGBpqCwEw==", "path": "system.threading.timer/4.0.1", "hashPath": "system.threading.timer.4.0.1.nupkg.sha512"}, "System.Xml.ReaderWriter/4.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-ZIiLPsf67YZ9zgr31vzrFaYQqxRPX9cVHjtPSnmx4eN6lbS/yEyYNr2vs1doGDEscF0tjCZFsk9yUg1sC9e8tg==", "path": "system.xml.readerwriter/4.0.11", "hashPath": "system.xml.readerwriter.4.0.11.nupkg.sha512"}, "System.Xml.XDocument/4.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-Mk2mKmPi0nWaoiYeotq1dgeNK1fqWh61+EK+w4Wu8SWuTYLzpUnschb59bJtGywaPq7SmTuPf44wrXRwbIrukg==", "path": "system.xml.xdocument/4.0.11", "hashPath": "system.xml.xdocument.4.0.11.nupkg.sha512"}, "Telegram.Bot/19.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Q16IOitgjGoaJOuqgKQy0FeF+hr/ncmlX2esrhCC7aiyhSX7roYEriWaGAHkQZR8QzbImjFfl4eQh2IxcnOrPg==", "path": "telegram.bot/19.0.0", "hashPath": "telegram.bot.19.0.0.nupkg.sha512"}}}