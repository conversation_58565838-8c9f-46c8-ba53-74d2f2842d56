using System;
using System.Threading.Tasks;
using System.Windows;
using System.Diagnostics;

namespace InzoIB_Simple
{
    public partial class LoginWindow : Window
    {
        private string currentUserUid;
        private bool isAuthenticated = false;

        public LoginWindow()
        {
            InitializeComponent();
        }

        /// <summary>
        /// تهيئة نظام المصادقة المؤقت
        /// </summary>
        private void InitializeAuth()
        {
            Debug.WriteLine("✅ تم تهيئة نظام المصادقة المؤقت");
        }

        /// <summary>
        /// معالج حدث زر تسجيل الدخول
        /// </summary>
        private async void LoginButton_Click(object sender, RoutedEventArgs e)
        {
            await PerformLogin();
        }

        /// <summary>
        /// تنفيذ عملية تسجيل الدخول
        /// </summary>
        private async Task PerformLogin()
        {
            try
            {
                // إخفاء الرسائل السابقة
                HideMessages();

                // التحقق من صحة البيانات المدخلة
                string email = EmailTextBox.Text.Trim();
                string password = PasswordBox.Password;

                if (string.IsNullOrEmpty(email))
                {
                    ShowError("يرجى إدخال البريد الإلكتروني");
                    return;
                }

                if (string.IsNullOrEmpty(password))
                {
                    ShowError("يرجى إدخال كلمة المرور");
                    return;
                }

                // تعطيل زر تسجيل الدخول أثناء المعالجة
                LoginButton.IsEnabled = false;
                LoginButton.Content = "🔄 جاري تسجيل الدخول...";

                // محاولة تسجيل الدخول (نسخة مؤقتة بدون Firebase)
                await Task.Delay(1000); // محاكاة وقت المعالجة

                // التحقق من البيانات التجريبية
                if (email == "<EMAIL>" && password == "password123")
                {
                    currentUserUid = "demo_user_12345";
                    isAuthenticated = true;

                    // عرض معلومات المستخدم
                    ShowUserInfo();

                    Debug.WriteLine($"✅ تم تسجيل الدخول بنجاح - UID: {currentUserUid}");
                }
                else
                {
                    ShowError("فشل في تسجيل الدخول - بيانات غير صحيحة\nاستخدم: <EMAIL> / password123");
                }
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في تسجيل الدخول: {ex.Message}");
                Debug.WriteLine($"❌ خطأ عام في تسجيل الدخول: {ex.Message}");
            }
            finally
            {
                // إعادة تفعيل زر تسجيل الدخول
                LoginButton.IsEnabled = true;
                LoginButton.Content = "🚀 تسجيل الدخول";
            }
        }

        /// <summary>
        /// عرض معلومات المستخدم بعد تسجيل الدخول الناجح
        /// </summary>
        private void ShowUserInfo()
        {
            UidTextBox.Text = currentUserUid;
            AuthStatusTextBlock.Text = "✅ مصادق عليه - صلاحيات كاملة";
            UserInfoPanel.Visibility = Visibility.Visible;
            ContinueButton.IsEnabled = true;
        }

        /// <summary>
        /// عرض رسالة خطأ
        /// </summary>
        private void ShowError(string message)
        {
            ErrorTextBlock.Text = message;
            ErrorPanel.Visibility = Visibility.Visible;
            UserInfoPanel.Visibility = Visibility.Collapsed;
        }

        /// <summary>
        /// إخفاء جميع الرسائل
        /// </summary>
        private void HideMessages()
        {
            ErrorPanel.Visibility = Visibility.Collapsed;
            UserInfoPanel.Visibility = Visibility.Collapsed;
        }



        /// <summary>
        /// معالج حدث زر الانتقال إلى البيانات
        /// </summary>
        private void ContinueButton_Click(object sender, RoutedEventArgs e)
        {
            if (isAuthenticated && !string.IsNullOrEmpty(currentUserUid))
            {
                // فتح نافذة البيانات الرئيسية
                var dataWindow = new DataWindow(currentUserUid, "<EMAIL>");
                dataWindow.Show();

                // إخفاء نافذة تسجيل الدخول
                this.Hide();
            }
            else
            {
                ShowError("يرجى تسجيل الدخول أولاً");
            }
        }

        /// <summary>
        /// الحصول على UID المستخدم الحالي
        /// </summary>
        public string GetCurrentUserUid()
        {
            return currentUserUid;
        }

        /// <summary>
        /// التحقق من حالة المصادقة
        /// </summary>
        public bool IsAuthenticated()
        {
            return isAuthenticated;
        }



        /// <summary>
        /// معالج إغلاق النافذة
        /// </summary>
        protected override void OnClosed(EventArgs e)
        {
            // تنظيف الموارد
            base.OnClosed(e);
        }
    }
}
