using System;
using System.Threading.Tasks;
using System.Windows;
using Firebase.Auth;
using System.Diagnostics;

namespace InzoIB_Simple
{
    public partial class LoginWindow : Window
    {
        private FirebaseAuthProvider authProvider;
        private FirebaseAuthLink authLink;
        private string currentUserUid;
        private bool isAuthenticated = false;

        public LoginWindow()
        {
            InitializeComponent();
            InitializeFirebaseAuth();
        }

        /// <summary>
        /// تهيئة Firebase Authentication
        /// </summary>
        private void InitializeFirebaseAuth()
        {
            try
            {
                // استخدام Firebase API Key - يجب استبداله بالمفتاح الصحيح
                string firebaseApiKey = "AIzaSyBvOyiA02_FEuaPiH84YaLDbP7VXSMXnGI"; // مفتاح تجريبي
                authProvider = new FirebaseAuthProvider(new FirebaseConfig(firebaseApiKey));
                Debug.WriteLine("✅ تم تهيئة Firebase Authentication");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ خطأ في تهيئة Firebase Auth: {ex.Message}");
                ShowError($"خطأ في تهيئة النظام: {ex.Message}");
            }
        }

        /// <summary>
        /// معالج حدث زر تسجيل الدخول
        /// </summary>
        private async void LoginButton_Click(object sender, RoutedEventArgs e)
        {
            await PerformLogin();
        }

        /// <summary>
        /// تنفيذ عملية تسجيل الدخول
        /// </summary>
        private async Task PerformLogin()
        {
            try
            {
                // إخفاء الرسائل السابقة
                HideMessages();

                // التحقق من صحة البيانات المدخلة
                string email = EmailTextBox.Text.Trim();
                string password = PasswordBox.Password;

                if (string.IsNullOrEmpty(email))
                {
                    ShowError("يرجى إدخال البريد الإلكتروني");
                    return;
                }

                if (string.IsNullOrEmpty(password))
                {
                    ShowError("يرجى إدخال كلمة المرور");
                    return;
                }

                // تعطيل زر تسجيل الدخول أثناء المعالجة
                LoginButton.IsEnabled = false;
                LoginButton.Content = "🔄 جاري تسجيل الدخول...";

                // محاولة تسجيل الدخول
                authLink = await authProvider.SignInWithEmailAndPasswordAsync(email, password);
                
                if (authLink != null && authLink.User != null)
                {
                    currentUserUid = authLink.User.LocalId;
                    isAuthenticated = true;

                    // عرض معلومات المستخدم
                    ShowUserInfo();
                    
                    Debug.WriteLine($"✅ تم تسجيل الدخول بنجاح - UID: {currentUserUid}");
                }
                else
                {
                    ShowError("فشل في تسجيل الدخول - بيانات غير صحيحة");
                }
            }
            catch (FirebaseAuthException authEx)
            {
                string errorMessage = GetFirebaseErrorMessage(authEx.Reason);
                ShowError(errorMessage);
                Debug.WriteLine($"❌ خطأ Firebase Auth: {authEx.Reason} - {authEx.Message}");
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في تسجيل الدخول: {ex.Message}");
                Debug.WriteLine($"❌ خطأ عام في تسجيل الدخول: {ex.Message}");
            }
            finally
            {
                // إعادة تفعيل زر تسجيل الدخول
                LoginButton.IsEnabled = true;
                LoginButton.Content = "🚀 تسجيل الدخول";
            }
        }

        /// <summary>
        /// عرض معلومات المستخدم بعد تسجيل الدخول الناجح
        /// </summary>
        private void ShowUserInfo()
        {
            UidTextBox.Text = currentUserUid;
            AuthStatusTextBlock.Text = "✅ مصادق عليه - صلاحيات كاملة";
            UserInfoPanel.Visibility = Visibility.Visible;
        }

        /// <summary>
        /// عرض رسالة خطأ
        /// </summary>
        private void ShowError(string message)
        {
            ErrorTextBlock.Text = message;
            ErrorPanel.Visibility = Visibility.Visible;
            UserInfoPanel.Visibility = Visibility.Collapsed;
        }

        /// <summary>
        /// إخفاء جميع الرسائل
        /// </summary>
        private void HideMessages()
        {
            ErrorPanel.Visibility = Visibility.Collapsed;
            UserInfoPanel.Visibility = Visibility.Collapsed;
        }

        /// <summary>
        /// تحويل رسائل خطأ Firebase إلى العربية
        /// </summary>
        private string GetFirebaseErrorMessage(AuthErrorReason reason)
        {
            switch (reason)
            {
                case AuthErrorReason.InvalidEmailAddress:
                    return "عنوان البريد الإلكتروني غير صحيح";
                case AuthErrorReason.WrongPassword:
                    return "كلمة المرور غير صحيحة";
                case AuthErrorReason.UserNotFound:
                    return "المستخدم غير موجود";
                case AuthErrorReason.UserDisabled:
                    return "تم تعطيل هذا المستخدم";
                case AuthErrorReason.TooManyAttempts:
                    return "محاولات كثيرة جداً، يرجى المحاولة لاحقاً";
                case AuthErrorReason.NetworkRequestFailed:
                    return "خطأ في الشبكة، تحقق من الاتصال بالإنترنت";
                case AuthErrorReason.InvalidAPIKey:
                    return "مفتاح API غير صحيح";
                default:
                    return $"خطأ في المصادقة: {reason}";
            }
        }

        /// <summary>
        /// معالج حدث زر الانتقال إلى البيانات
        /// </summary>
        private void ContinueButton_Click(object sender, RoutedEventArgs e)
        {
            if (isAuthenticated && !string.IsNullOrEmpty(currentUserUid))
            {
                // فتح نافذة البيانات الرئيسية
                var dataWindow = new DataWindow(currentUserUid, authLink);
                dataWindow.Show();
                
                // إخفاء نافذة تسجيل الدخول
                this.Hide();
            }
            else
            {
                ShowError("يرجى تسجيل الدخول أولاً");
            }
        }

        /// <summary>
        /// الحصول على UID المستخدم الحالي
        /// </summary>
        public string GetCurrentUserUid()
        {
            return currentUserUid;
        }

        /// <summary>
        /// التحقق من حالة المصادقة
        /// </summary>
        public bool IsAuthenticated()
        {
            return isAuthenticated;
        }

        /// <summary>
        /// الحصول على رابط المصادقة
        /// </summary>
        public FirebaseAuthLink GetAuthLink()
        {
            return authLink;
        }

        /// <summary>
        /// معالج إغلاق النافذة
        /// </summary>
        protected override void OnClosed(EventArgs e)
        {
            // تنظيف الموارد
            authLink?.Dispose();
            base.OnClosed(e);
        }
    }
}
