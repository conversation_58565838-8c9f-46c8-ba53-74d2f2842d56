<Window x:Class="InzoIB_Simple.LoginWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="تسجيل الدخول - Inzo IB" Height="500" Width="400"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        ResizeMode="NoResize"
        WindowStyle="SingleBorderWindow">

    <Grid Background="#f5f5f5">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#161642" Padding="20">
            <StackPanel HorizontalAlignment="Center">
                <TextBlock Text="🔐 تسجيل الدخول" 
                          FontSize="24" FontWeight="Bold" 
                          Foreground="White" 
                          HorizontalAlignment="Center" 
                          Margin="0,0,0,5"/>
                <TextBlock Text="Inzo IB v7.4" 
                          FontSize="14" 
                          Foreground="#ccc" 
                          HorizontalAlignment="Center"/>
            </StackPanel>
        </Border>

        <!-- Login Form -->
        <Border Grid.Row="1" Background="White" Margin="20" CornerRadius="10" Padding="30">
            <Border.Effect>
                <DropShadowEffect Color="Black" Direction="315" ShadowDepth="5" Opacity="0.1"/>
            </Border.Effect>
            
            <StackPanel>
                <!-- Email Field -->
                <TextBlock Text="📧 البريد الإلكتروني:" 
                          FontSize="14" FontWeight="Bold" 
                          Foreground="#161642" 
                          Margin="0,0,0,5"/>
                <TextBox x:Name="EmailTextBox" 
                        FontSize="14" 
                        Padding="10" 
                        Height="40" 
                        BorderBrush="#ddd" 
                        BorderThickness="2" 
                        Margin="0,0,0,20"
                        Text="<EMAIL>"/>

                <!-- Password Field -->
                <TextBlock Text="🔒 كلمة المرور:" 
                          FontSize="14" FontWeight="Bold" 
                          Foreground="#161642" 
                          Margin="0,0,0,5"/>
                <PasswordBox x:Name="PasswordBox" 
                            FontSize="14" 
                            Padding="10" 
                            Height="40" 
                            BorderBrush="#ddd" 
                            BorderThickness="2" 
                            Margin="0,0,0,20"/>

                <!-- Login Button -->
                <Button x:Name="LoginButton" 
                       Content="🚀 تسجيل الدخول" 
                       Background="#161642" 
                       Foreground="White" 
                       FontSize="16" FontWeight="Bold" 
                       Height="45" 
                       Margin="0,10,0,20" 
                       Click="LoginButton_Click"
                       Cursor="Hand">
                    <Button.Style>
                        <Style TargetType="Button">
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate TargetType="Button">
                                        <Border Background="{TemplateBinding Background}" 
                                               CornerRadius="5" 
                                               BorderThickness="0">
                                            <ContentPresenter HorizontalAlignment="Center" 
                                                            VerticalAlignment="Center"/>
                                        </Border>
                                        <ControlTemplate.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Background" Value="#2d2d44"/>
                                            </Trigger>
                                        </ControlTemplate.Triggers>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                        </Style>
                    </Button.Style>
                </Button>

                <!-- User Info Display -->
                <Border x:Name="UserInfoPanel" 
                       Background="#f0f8ff" 
                       BorderBrush="#4CAF50" 
                       BorderThickness="2" 
                       CornerRadius="5" 
                       Padding="15" 
                       Margin="0,10,0,0"
                       Visibility="Collapsed">
                    <StackPanel>
                        <TextBlock Text="✅ تم تسجيل الدخول بنجاح" 
                                  FontSize="14" FontWeight="Bold" 
                                  Foreground="#4CAF50" 
                                  HorizontalAlignment="Center" 
                                  Margin="0,0,0,10"/>
                        
                        <TextBlock Text="🆔 معرف المستخدم (UID):" 
                                  FontSize="12" FontWeight="Bold" 
                                  Foreground="#161642" 
                                  Margin="0,0,0,5"/>
                        <TextBox x:Name="UidTextBox" 
                                FontSize="11" 
                                Padding="5" 
                                IsReadOnly="True" 
                                Background="#f9f9f9" 
                                BorderBrush="#ddd" 
                                Margin="0,0,0,10"/>
                        
                        <TextBlock Text="🔐 حالة الصلاحية:" 
                                  FontSize="12" FontWeight="Bold" 
                                  Foreground="#161642" 
                                  Margin="0,0,0,5"/>
                        <TextBlock x:Name="AuthStatusTextBlock" 
                                  FontSize="12" 
                                  Foreground="#4CAF50" 
                                  FontWeight="Bold"/>
                        
                        <Button x:Name="ContinueButton" 
                               Content="📋 الانتقال إلى البيانات" 
                               Background="#4CAF50" 
                               Foreground="White" 
                               FontSize="14" FontWeight="Bold" 
                               Height="35" 
                               Margin="0,15,0,0" 
                               Click="ContinueButton_Click"
                               Cursor="Hand">
                            <Button.Style>
                                <Style TargetType="Button">
                                    <Setter Property="Template">
                                        <Setter.Value>
                                            <ControlTemplate TargetType="Button">
                                                <Border Background="{TemplateBinding Background}" 
                                                       CornerRadius="5" 
                                                       BorderThickness="0">
                                                    <ContentPresenter HorizontalAlignment="Center" 
                                                                    VerticalAlignment="Center"/>
                                                </Border>
                                                <ControlTemplate.Triggers>
                                                    <Trigger Property="IsMouseOver" Value="True">
                                                        <Setter Property="Background" Value="#45a049"/>
                                                    </Trigger>
                                                </ControlTemplate.Triggers>
                                            </ControlTemplate>
                                        </Setter.Value>
                                    </Setter>
                                </Style>
                            </Button.Style>
                        </Button>
                    </StackPanel>
                </Border>

                <!-- Error Message -->
                <Border x:Name="ErrorPanel" 
                       Background="#ffebee" 
                       BorderBrush="#f44336" 
                       BorderThickness="2" 
                       CornerRadius="5" 
                       Padding="15" 
                       Margin="0,10,0,0"
                       Visibility="Collapsed">
                    <TextBlock x:Name="ErrorTextBlock" 
                              FontSize="12" 
                              Foreground="#f44336" 
                              TextWrapping="Wrap"/>
                </Border>
            </StackPanel>
        </Border>

        <!-- Footer -->
        <Border Grid.Row="2" Background="#161642" Padding="10">
            <TextBlock Text="© 2024 Inzo IB - نظام إدارة المحتوى" 
                      FontSize="12" 
                      Foreground="#ccc" 
                      HorizontalAlignment="Center"/>
        </Border>
    </Grid>
</Window>
